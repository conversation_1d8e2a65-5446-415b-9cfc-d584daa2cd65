@tailwind base;
@tailwind components;
@tailwind utilities;

/* ConnectaBuild Design System - Exact color palette */

@layer base {
  :root {
    --background: 240 20% 98%;
    --foreground: 213 40% 25%;

    --card: 0 0% 100%;
    --card-foreground: 213 40% 25%;

    --popover: 0 0% 100%;
    --popover-foreground: 213 40% 25%;

    /* Navy Blue - ConnectaBuild primary brand color */
    --primary: 213 65% 28%;
    --primary-foreground: 0 0% 100%;

    /* Cyan/Turquoise - ConnectaBuild accent color */
    --secondary: 186 100% 50%;
    --secondary-foreground: 213 65% 28%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 186 100% 50%;
    --accent-foreground: 213 65% 28%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    /* ConnectaBuild specific colors */
    --connecta-navy: 213 65% 28%;
    --connecta-cyan: 186 100% 50%;
    --connecta-light-cyan: 186 100% 85%;
    --connecta-dark-navy: 213 75% 20%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 213 65% 28%;

    --radius: 0.5rem;

    /* ConnectaBuild Design System - Generic Rules */
    
    /* Typography Scale */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;

    /* Spacing Scale */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    
    /* Transitions */
    --transition-base: 150ms ease-in-out;
    --transition-colors: color 150ms ease-in-out, background-color 150ms ease-in-out, border-color 150ms ease-in-out;
    
    /* Z-index layers */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal: 1040;
    --z-popover: 1050;
    --z-tooltip: 1060;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* ConnectaBuild Generic Component Classes */
@layer components {
  /* Button Variants */
  .btn-primary {
    @apply bg-connecta-dark-navy hover:bg-connecta-navy text-white font-medium transition-colors duration-150 px-4 py-2 rounded-md;
  }
  
  .btn-secondary {
    @apply bg-connecta-cyan hover:bg-connecta-light-cyan text-connecta-navy font-medium transition-colors duration-150 px-4 py-2 rounded-md;
  }
  
  .btn-outline {
    @apply border border-border hover:bg-muted text-foreground font-medium transition-colors duration-150 px-4 py-2 rounded-md;
  }

  /* Status Badges */
  .status-draft {
    @apply bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium;
  }
  
  .status-published {
    @apply bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium;
  }
  
  .status-completed {
    @apply bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium;
  }

  /* Navigation Items */
  .nav-item {
    @apply flex items-center gap-3 px-4 py-3 text-muted-foreground hover:text-connecta-navy hover:bg-muted/50 transition-colors duration-150 rounded-md;
  }
  
  .nav-item.active {
    @apply text-connecta-navy bg-connecta-light-cyan/20 border-r-2 border-connecta-cyan;
  }

  /* Project Cards */
  .project-card {
    @apply bg-card border border-border rounded-lg p-4 hover:shadow-md transition-shadow duration-150;
  }

  /* Search and Filter */
  .search-input {
    @apply w-full px-4 py-2 border border-border rounded-md focus:border-connecta-cyan focus:ring-1 focus:ring-connecta-cyan/20 transition-colors duration-150;
  }

  /* Tab Navigation */
  .tab-nav {
    @apply flex border-b border-border;
  }
  
  .tab-item {
    @apply px-4 py-2 text-muted-foreground hover:text-foreground border-b-2 border-transparent transition-colors duration-150;
  }
  
  .tab-item.active {
    @apply text-connecta-cyan border-connecta-cyan font-medium;
  }
}