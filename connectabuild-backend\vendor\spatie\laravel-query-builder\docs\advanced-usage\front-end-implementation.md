---
title: Front-end implementation
weight: 6
---

If you're interested in building query urls on the front-end to match this package, you could use one of the below:

- Standalone: [elodo package](https://www.npmjs.com/package/elodo) by [<PERSON>](https://github.com/MaximV<PERSON>hove).
- Vue: [vue-api-query package](https://github.com/robsontenorio/vue-api-query) by [<PERSON>](https://github.com/robsontenorio).
- Vue + Inertia.js: [inertiajs-tables-laravel-query-builder](https://github.com/protonemedia/inertiajs-tables-laravel-query-builder) by [
<PERSON>](https://github.com/pascalbaljet).
- React: [cogent-js package](https://www.npmjs.com/package/cogent-js) by [<PERSON>](https://github.com/joelwmale).
- Typescript: [query-builder-ts package](https://www.npmjs.com/package/@vortechron/query-builder-ts) by [<PERSON><PERSON>](https://www.npmjs.com/~vortechron)
- Typescript + React [react-query-builder](https://www.npmjs.com/package/@cgarciagarcia/react-query-builder) by [Carlos Garcia](https://github.com/cgarciagarcia)
