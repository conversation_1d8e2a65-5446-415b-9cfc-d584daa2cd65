<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Company;

class CompanySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $companies = [
            [
                'name' => 'Metro Plumbing Solutions',
                'address' => '123 Industrial Ave, Sydney NSW 2000',
                'phone' => '0409536295',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Sydney Electricals',
                'address' => '456 Power Street, Sydney NSW 2001',
                'phone' => '02 9876 5432',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'ConnectaBuild Pty Ltd',
                'address' => '789 Construction Blvd, Sydney NSW 2002',
                'phone' => '02 1234 5678',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Elite HVAC Services',
                'address' => '321 Climate Road, Sydney NSW 2003',
                'phone' => '02 5555 1234',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Precision Structural Engineering',
                'address' => '654 Foundation Street, Sydney NSW 2004',
                'phone' => '02 7777 8888',
                'email' => '<EMAIL>',
            ],
        ];

        foreach ($companies as $company) {
            Company::firstOrCreate(
                ['name' => $company['name']],
                $company
            );
        }
    }
}
