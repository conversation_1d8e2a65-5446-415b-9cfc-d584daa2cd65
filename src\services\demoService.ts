import { User, Project, AuthResponse } from '@/lib/api';

// Données de démonstration
const demoUsers: User[] = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '0409536295',
    company: {
      id: 1,
      name: 'ConnectaBuild Pty Ltd',
      address: '789 Construction Blvd, Sydney NSW 2002',
      phone: '02 1234 5678',
      email: '<EMAIL>'
    },
    discipline: {
      id: 1,
      name: '<PERSON>',
      color: '#32CD32'
    },
    unread_notifications_count: 3
  },
  {
    id: 2,
    name: '<PERSON>',
    email: 'jam<PERSON><PERSON><PERSON><PERSON>@metroplumbingsolutions.com',
    phone: '0409536295',
    company: {
      id: 2,
      name: 'Metro Plumbing Solutions',
      address: '123 Industrial Ave, Sydney NSW 2000',
      phone: '0409536295',
      email: '<EMAIL>'
    },
    discipline: {
      id: 2,
      name: 'Plumbing',
      color: '#0066CC'
    },
    unread_notifications_count: 1
  },
  {
    id: 3,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '02 9876 5432',
    company: {
      id: 3,
      name: 'Sydney Electricals',
      address: '456 Power Street, Sydney NSW 2001',
      phone: '02 9876 5432',
      email: '<EMAIL>'
    },
    discipline: {
      id: 3,
      name: 'Electrical',
      color: '#FFD700'
    },
    unread_notifications_count: 0
  }
];

const demoProjects: Project[] = [
  {
    id: 1,
    name: 'Dee Why Suites',
    address: '8 Oaks Avenue, Dee Why, 2099 NSW',
    description: 'Luxury residential development with modern amenities',
    status: 'draft',
    created_by: 1,
    creator: demoUsers[0],
    created_at: '2024-07-01T10:00:00Z',
    updated_at: '2024-07-11T15:30:00Z'
  },
  {
    id: 2,
    name: 'Chatswood Mall Expansion',
    address: '17 Victoria Avenue, Chatswood, NSW 2067',
    description: 'Commercial expansion project for retail spaces',
    status: 'published',
    created_by: 1,
    creator: demoUsers[0],
    cover_image: 'https://images.unsplash.com/photo-1486718448742-163732cd1544?w=400&h=300&fit=crop',
    created_at: '2024-06-15T09:00:00Z',
    updated_at: '2024-07-10T14:20:00Z'
  },
  {
    id: 3,
    name: 'Parramatta Business Park',
    address: '15 Smith Street, Parramatta, NSW 2150',
    description: 'Modern office complex with sustainable design',
    status: 'published',
    created_by: 2,
    creator: demoUsers[1],
    cover_image: 'https://images.unsplash.com/photo-1497604401993-f2e922e5cb0a?w=400&h=300&fit=crop',
    created_at: '2024-06-01T08:00:00Z',
    updated_at: '2024-07-09T16:45:00Z'
  },
  {
    id: 4,
    name: 'The Bondi Residences',
    address: '100 Campbell Parade, Bondi Beach, NSW 2026',
    description: 'Beachfront residential towers with ocean views',
    status: 'published',
    created_by: 1,
    creator: demoUsers[0],
    cover_image: 'https://images.unsplash.com/photo-1449157291145-7efd050a4d0e?w=400&h=300&fit=crop',
    created_at: '2024-05-20T07:30:00Z',
    updated_at: '2024-07-08T11:15:00Z'
  },
  {
    id: 5,
    name: 'East Sydney Apartments',
    address: '28 George Street, Darlinghurst, NSW 2010',
    description: 'Urban living apartments in the heart of Sydney',
    status: 'published',
    created_by: 2,
    creator: demoUsers[1],
    cover_image: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=300&fit=crop',
    created_at: '2024-05-10T06:00:00Z',
    updated_at: '2024-07-07T13:30:00Z'
  },
  {
    id: 6,
    name: 'Redfern Tower',
    address: '45-47 Walker Street, Redfern, NSW 2016',
    description: 'Mixed-use development with retail and residential',
    status: 'published',
    created_by: 3,
    creator: demoUsers[2],
    cover_image: 'https://images.unsplash.com/photo-1518005020951-eccb494ad742?w=400&h=300&fit=crop',
    created_at: '2024-04-25T05:45:00Z',
    updated_at: '2024-07-06T10:20:00Z'
  },
  {
    id: 7,
    name: 'Glebe Waterfront Development',
    address: '45 Bridge Road, Glebe, NSW 2037',
    description: 'Waterfront development with marina access',
    status: 'completed',
    created_by: 1,
    creator: demoUsers[0],
    cover_image: 'https://images.unsplash.com/photo-1493397212122-2b85dda8106b?w=400&h=300&fit=crop',
    created_at: '2024-03-01T04:00:00Z',
    updated_at: '2024-06-30T17:00:00Z'
  }
];

export const demoService = {
  // Simulation de l'authentification
  async login(email: string, password: string): Promise<AuthResponse> {
    // Simuler un délai réseau
    await new Promise(resolve => setTimeout(resolve, 1000));

    const user = demoUsers.find(u => u.email === email);
    
    if (!user || password !== 'password123') {
      throw new Error('Invalid credentials');
    }

    const token = 'demo-token-' + Date.now();
    
    return {
      user,
      token,
      token_type: 'Bearer'
    };
  },

  // Simulation de récupération des projets
  async getProjects(): Promise<{ data: Project[] }> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return {
      data: demoProjects
    };
  },

  // Simulation de récupération de l'utilisateur actuel
  async getCurrentUser(): Promise<User> {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // Retourner le premier utilisateur par défaut
    return demoUsers[0];
  }
};
