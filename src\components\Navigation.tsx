import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { LogOut, User, Settings } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import connectaIcon from "@/assets/connecta-logo-icon.png";

const Navigation = () => {
  const navigate = useNavigate();
  const { user, logout, isLoggingOut } = useAuth();

  const handleLogout = () => {
    logout(undefined, {
      onSuccess: () => {
        navigate('/login');
      }
    });
  };

  return (
    <nav className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Logo */}
        <div className="flex items-center space-x-4">
          <img 
            src={connectaIcon} 
            alt="ConnectaBuild" 
            className="h-8 w-auto"
          />
          <h1 className="text-xl font-bold text-connecta-dark-navy">
            ConnectaBuild
          </h1>
        </div>

        {/* User Menu */}
        <div className="flex items-center space-x-4">
          {user && (
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <User className="w-5 h-5 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">
                  {user.name}
                </span>
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/settings')}
                className="text-gray-600 hover:text-gray-900"
              >
                <Settings className="w-4 h-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleLogout}
                disabled={isLoggingOut}
                className="text-gray-600 hover:text-red-600"
              >
                <LogOut className="w-4 h-4" />
                {isLoggingOut ? "..." : "Déconnexion"}
              </Button>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
