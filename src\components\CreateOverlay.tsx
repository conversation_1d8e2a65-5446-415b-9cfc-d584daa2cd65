import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus, Upload, X } from 'lucide-react';
import { useCreateOverlay } from '@/hooks/useOverlays';

interface CreateOverlayProps {
  levelId: number;
  onSuccess?: () => void;
}

// Disciplines disponibles (à récupérer depuis l'API plus tard)
const disciplines = [
  { id: 1, name: 'Electrical', color: '#FFD700' },
  { id: 2, name: 'Plumbing', color: '#0066CC' },
  { id: 3, name: 'HVAC', color: '#FF6B35' },
  { id: 4, name: 'Structural', color: '#8B4513' },
  { id: 5, name: 'Architectural', color: '#32CD32' },
  { id: 6, name: 'Fire Safety', color: '#FF0000' },
  { id: 7, name: 'Security', color: '#800080' },
  { id: 8, name: 'Telecommunications', color: '#00CED1' },
];

const CreateOverlay = ({ levelId, onSuccess }: CreateOverlayProps) => {
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    discipline_id: '',
    color: '',
    file: null as File | null,
  });

  const createOverlayMutation = useCreateOverlay();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.file || !formData.name || !formData.discipline_id) {
      alert('Please fill in all required fields');
      return;
    }

    createOverlayMutation.mutate({
      levelId,
      data: {
        name: formData.name,
        discipline_id: parseInt(formData.discipline_id),
        color: formData.color || undefined,
        file: formData.file,
      }
    }, {
      onSuccess: () => {
        setFormData({ name: '', discipline_id: '', color: '', file: null });
        setOpen(false);
        onSuccess?.();
      }
    });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, file }));
    }
  };

  const handleDisciplineChange = (value: string) => {
    const discipline = disciplines.find(d => d.id.toString() === value);
    setFormData(prev => ({
      ...prev,
      discipline_id: value,
      color: discipline?.color || ''
    }));
  };

  const removeFile = () => {
    setFormData(prev => ({ ...prev, file: null }));
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="flex items-center gap-2">
          <Plus className="w-4 h-4" />
          Create Overlay
        </Button>
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Create New Overlay</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Name */}
          <div>
            <Label htmlFor="name">Overlay Name *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter overlay name"
              required
            />
          </div>

          {/* Discipline */}
          <div>
            <Label htmlFor="discipline">Discipline *</Label>
            <Select value={formData.discipline_id} onValueChange={handleDisciplineChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select discipline" />
              </SelectTrigger>
              <SelectContent>
                {disciplines.map((discipline) => (
                  <SelectItem key={discipline.id} value={discipline.id.toString()}>
                    <div className="flex items-center gap-2">
                      <div 
                        className="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: discipline.color }}
                      />
                      {discipline.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Color */}
          <div>
            <Label htmlFor="color">Color (optional)</Label>
            <div className="flex items-center gap-2">
              <Input
                id="color"
                type="color"
                value={formData.color}
                onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                className="w-16 h-10 p-1"
              />
              <Input
                value={formData.color}
                onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                placeholder="#000000"
                className="flex-1"
              />
            </div>
          </div>

          {/* File Upload */}
          <div>
            <Label htmlFor="file">File *</Label>
            <div className="mt-1">
              {formData.file ? (
                <div className="flex items-center justify-between p-3 border border-gray-300 rounded-md bg-gray-50">
                  <div className="flex items-center gap-2">
                    <Upload className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-700">{formData.file.name}</span>
                    <span className="text-xs text-gray-500">
                      ({(formData.file.size / 1024 / 1024).toFixed(2)} MB)
                    </span>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={removeFile}
                    className="text-red-500 hover:text-red-700"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              ) : (
                <div className="border-2 border-dashed border-gray-300 rounded-md p-6 text-center">
                  <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600 mb-2">
                    Click to upload or drag and drop
                  </p>
                  <p className="text-xs text-gray-500">
                    PDF, DWG, DXF, PNG, JPG (max 10MB)
                  </p>
                  <Input
                    id="file"
                    type="file"
                    onChange={handleFileChange}
                    accept=".pdf,.dwg,.dxf,.png,.jpg,.jpeg"
                    className="mt-2"
                    required
                  />
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={createOverlayMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={createOverlayMutation.isPending}
            >
              {createOverlayMutation.isPending ? 'Creating...' : 'Create Overlay'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateOverlay;
