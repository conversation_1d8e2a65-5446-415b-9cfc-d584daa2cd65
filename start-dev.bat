@echo off
echo Starting ConnectaBuild Development Environment...

echo.
echo 1. Starting <PERSON><PERSON>end...
start "Laravel Backend" cmd /k "cd connectabuild-backend && php artisan serve"

echo.
echo 2. Waiting for backend to start...
timeout /t 5 /nobreak > nul

echo.
echo 3. Starting React Frontend...
start "React Frontend" cmd /k "npm run dev"

echo.
echo Development environment started!
echo Backend: http://localhost:8000
echo Frontend: http://localhost:5173
echo.
echo Press any key to close this window...
pause > nul
