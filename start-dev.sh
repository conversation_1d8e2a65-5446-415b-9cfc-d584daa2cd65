#!/bin/bash

echo "Starting ConnectaBuild Development Environment..."

echo ""
echo "1. Starting Laravel Backend..."
cd connectabuild-backend
php artisan serve &
BACKEND_PID=$!

echo ""
echo "2. Waiting for backend to start..."
sleep 5

echo ""
echo "3. Starting React Frontend..."
cd ..
npm run dev &
FRONTEND_PID=$!

echo ""
echo "Development environment started!"
echo "Backend: http://localhost:8000"
echo "Frontend: http://localhost:5173"
echo ""
echo "Press Ctrl+C to stop both servers..."

# Function to cleanup processes on exit
cleanup() {
    echo ""
    echo "Stopping servers..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    exit 0
}

# Trap Ctrl+C
trap cleanup INT

# Wait for processes
wait
