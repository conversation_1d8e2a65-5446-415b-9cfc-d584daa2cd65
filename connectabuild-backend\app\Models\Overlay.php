<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Overlay extends Model
{
    use HasFactory;

    protected $fillable = [
        'level_id',
        'discipline_id',
        'name',
        'color',
        'file_path',
        'created_by',
    ];

    /**
     * Get the level that owns the overlay.
     */
    public function level(): BelongsTo
    {
        return $this->belongsTo(Level::class);
    }

    /**
     * Get the discipline that owns the overlay.
     */
    public function discipline(): BelongsTo
    {
        return $this->belongsTo(Discipline::class);
    }

    /**
     * Get the user who created the overlay.
     */
    public function creator(): BelongsT<PERSON>
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
