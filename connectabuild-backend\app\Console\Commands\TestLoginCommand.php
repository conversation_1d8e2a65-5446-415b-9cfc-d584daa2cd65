<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class TestLoginCommand extends Command
{
    protected $signature = 'test:login {email} {password}';
    protected $description = 'Test le processus de login étape par étape';

    public function handle()
    {
        $email = $this->argument('email');
        $password = $this->argument('password');
        
        $this->info("🧪 Test de login pour: $email");
        $this->info("Mot de passe: $password");
        $this->newLine();

        // Étape 1: Trouver l'utilisateur
        $this->info('1. Recherche de l\'utilisateur...');
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error('   ❌ Utilisateur non trouvé');
            return 1;
        }
        
        $this->line('   ✅ Utilisateur trouvé: ' . $user->name);
        $this->line('   - ID: ' . $user->id);
        $this->line('   - Email: ' . $user->email);
        $this->line('   - Hash stocké: ' . substr($user->password, 0, 20) . '...');
        $this->newLine();

        // Étape 2: Vérifier le mot de passe
        $this->info('2. Vérification du mot de passe...');
        $this->line('   - Mot de passe fourni: ' . $password);
        $this->line('   - Hash du mot de passe fourni: ' . Hash::make($password));
        
        $isValid = Hash::check($password, $user->password);
        
        if ($isValid) {
            $this->line('   ✅ Mot de passe correct');
        } else {
            $this->error('   ❌ Mot de passe incorrect');
            
            // Test avec différents mots de passe
            $this->info('   Tentatives avec d\'autres mots de passe:');
            $testPasswords = ['password123', 'password', '123456', 'admin'];
            
            foreach ($testPasswords as $testPwd) {
                $testResult = Hash::check($testPwd, $user->password);
                $status = $testResult ? '✅' : '❌';
                $this->line("     $status '$testPwd'");
            }
        }
        $this->newLine();

        // Étape 3: Test de création de token
        if ($isValid) {
            $this->info('3. Test de création de token...');
            try {
                $token = $user->createToken('test-token');
                $this->line('   ✅ Token créé: ' . substr($token->plainTextToken, 0, 20) . '...');
            } catch (\Exception $e) {
                $this->error('   ❌ Erreur création token: ' . $e->getMessage());
            }
        }

        return 0;
    }
}
