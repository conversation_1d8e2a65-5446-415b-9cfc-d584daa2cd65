const axios = require('axios');

async function testProjects() {
  console.log('🧪 Test de l\'API des projets...\n');

  try {
    // 1. Login pour obtenir un token
    console.log('1. Login...');
    const loginResponse = await axios.post('http://localhost:8000/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });

    const token = loginResponse.data.token;
    console.log('✅ Login réussi, token obtenu');

    // 2. Récupérer les projets
    console.log('\n2. Récupération des projets...');
    const projectsResponse = await axios.get('http://localhost:8000/api/projects', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json'
      }
    });

    console.log('✅ Projets récupérés');
    console.log('Status:', projectsResponse.status);
    console.log('Nombre de projets:', projectsResponse.data.data.length);
    console.log('Total:', projectsResponse.data.total);
    
    if (projectsResponse.data.data.length > 0) {
      console.log('\nPremier projet:');
      const firstProject = projectsResponse.data.data[0];
      console.log('- ID:', firstProject.id);
      console.log('- Nom:', firstProject.name);
      console.log('- Adresse:', firstProject.address);
      console.log('- Status:', firstProject.status);
      console.log('- Créateur:', firstProject.creator?.name);
    } else {
      console.log('\n❌ Aucun projet trouvé');
    }

  } catch (error) {
    console.log('❌ Erreur:');
    console.log('Status:', error.response?.status);
    console.log('Message:', error.message);
    console.log('Data:', error.response?.data);
  }
}

testProjects();
