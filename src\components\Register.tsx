import { useState } from "react";
import { useNavigate, <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAuth } from "@/hooks/useAuth";
import connectaIcon from "@/assets/connecta-logo-icon.png";
import connectaIllustration from "@/assets/connecta-illustration.png";

const Register = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    password_confirmation: "",
    phone: "",
    company_id: "",
    discipline_id: ""
  });
  
  const navigate = useNavigate();
  const { register, isRegistering, registerError } = useAuth();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.password !== formData.password_confirmation) {
      alert("Passwords do not match");
      return;
    }
    
    register({
      ...formData,
      company_id: formData.company_id ? parseInt(formData.company_id) : undefined,
      discipline_id: formData.discipline_id ? parseInt(formData.discipline_id) : undefined,
    }, {
      onSuccess: () => {
        navigate('/projects');
      }
    });
  };

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="min-h-screen flex">
      {/* Left side - Illustration */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-connecta-navy to-connecta-dark-navy items-center justify-center p-12">
        <div className="max-w-md text-center">
          <img 
            src={connectaIllustration} 
            alt="ConnectaBuild Illustration" 
            className="w-full h-auto mb-8"
          />
          <h2 className="text-3xl font-bold text-white mb-4">
            Join ConnectaBuild
          </h2>
          <p className="text-connecta-light text-lg">
            Collaborate effectively on your construction projects with our innovative platform.
          </p>
        </div>
      </div>

      {/* Right side - Registration Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
        <div className="w-full max-w-md space-y-8">
          {/* Logo */}
          <div className="text-center">
            <img 
              src={connectaIcon} 
              alt="ConnectaBuild" 
              className="h-12 w-auto mx-auto mb-4"
            />
            <h1 className="text-2xl font-bold text-foreground">
              Create Account
            </h1>
            <p className="text-muted-foreground mt-2">
              Fill in the information below to get started
            </p>
          </div>

          {/* Registration Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  required
                  className="mt-1"
                  placeholder="Your full name"
                />
              </div>

              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleChange('email', e.target.value)}
                  required
                  className="mt-1"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleChange('phone', e.target.value)}
                  className="mt-1"
                  placeholder="0X XX XX XX XX"
                />
              </div>

              <div>
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={formData.password}
                  onChange={(e) => handleChange('password', e.target.value)}
                  required
                  className="mt-1"
                  placeholder="••••••••"
                />
              </div>

              <div>
                <Label htmlFor="password_confirmation">Confirm Password</Label>
                <Input
                  id="password_confirmation"
                  type="password"
                  value={formData.password_confirmation}
                  onChange={(e) => handleChange('password_confirmation', e.target.value)}
                  required
                  className="mt-1"
                  placeholder="••••••••"
                />
              </div>

              <div>
                <Label htmlFor="discipline">Discipline (optional)</Label>
                <Select onValueChange={(value) => handleChange('discipline_id', value)}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select your discipline" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">Electrical</SelectItem>
                    <SelectItem value="2">Plumbing</SelectItem>
                    <SelectItem value="3">HVAC</SelectItem>
                    <SelectItem value="4">Structural</SelectItem>
                    <SelectItem value="5">Architectural</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {registerError && (
              <div className="text-red-500 text-sm">
                {registerError.message || 'Registration error'}
              </div>
            )}

            <Button
              type="submit"
              disabled={isRegistering}
              className="w-full h-12 bg-connecta-dark-navy hover:bg-connecta-navy text-white font-medium text-lg disabled:opacity-50"
            >
              {isRegistering ? "Creating account..." : "Create Account"}
            </Button>
          </form>

          {/* Login Link */}
          <div className="text-center">
            <p className="text-muted-foreground">
              Already have an account?{" "}
              <Link
                to="/login"
                className="text-connecta-navy hover:text-connecta-dark-navy font-medium"
              >
                Sign In
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Register;
