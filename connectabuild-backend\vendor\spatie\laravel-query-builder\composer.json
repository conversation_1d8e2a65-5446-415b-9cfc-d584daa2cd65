{"name": "spatie/laravel-query-builder", "description": "Easily build Eloquent queries from API requests", "keywords": ["spatie", "laravel-query-builder"], "homepage": "https://github.com/spatie/laravel-query-builder", "license": "MIT", "support": {"issues": "https://github.com/spatie/laravel-query-builder/issues", "source": "https://github.com/spatie/laravel-query-builder"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "require": {"php": "^8.2", "illuminate/database": "^10.0|^11.0|^12.0", "illuminate/http": "^10.0|^11.0|^12.0", "illuminate/support": "^10.0|^11.0|^12.0", "spatie/laravel-package-tools": "^1.11"}, "require-dev": {"ext-json": "*", "mockery/mockery": "^1.4", "orchestra/testbench": "^7.0|^8.0|^10.0", "pestphp/pest": "^2.0|^3.7", "phpunit/phpunit": "^10.0|^11.5.3", "spatie/invade": "^2.0"}, "autoload": {"psr-4": {"Spatie\\QueryBuilder\\": "src", "Spatie\\QueryBuilder\\Database\\Factories\\": "database/factories"}}, "autoload-dev": {"psr-4": {"Spatie\\QueryBuilder\\Tests\\": "tests"}}, "scripts": {"test": "vendor/bin/pest", "test-coverage": "phpunit --coverage-html coverage", "analyse": "vendor/bin/phpstan analyse --ansi --memory-limit=4G", "baseline": "vendor/bin/phpstan analyse --generate-baseline --memory-limit=4G"}, "config": {"sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "extra": {"laravel": {"providers": ["Spatie\\QueryBuilder\\QueryBuilderServiceProvider"]}}, "minimum-stability": "dev", "prefer-stable": true}