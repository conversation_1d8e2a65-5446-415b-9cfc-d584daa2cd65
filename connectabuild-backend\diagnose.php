<?php

echo "🔍 Diagnostic ConnectaBuild Backend\n";
echo "==================================\n\n";

// 1. Vérifier l'environnement
echo "1. Environnement Laravel:\n";
echo "   - APP_ENV: " . env('APP_ENV', 'non défini') . "\n";
echo "   - APP_DEBUG: " . (env('APP_DEBUG') ? 'true' : 'false') . "\n";
echo "   - APP_URL: " . env('APP_URL', 'non défini') . "\n";
echo "   - DB_CONNECTION: " . env('DB_CONNECTION', 'non défini') . "\n\n";

// 2. Vérifier la base de données
echo "2. Base de données:\n";
try {
    $pdo = new PDO('sqlite:' . database_path('database.sqlite'));
    echo "   ✅ Connexion SQLite OK\n";
    
    // Compter les utilisateurs
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $userCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "   - Nombre d'utilisateurs: $userCount\n";
    
    // Lister les emails
    $stmt = $pdo->query("SELECT email FROM users LIMIT 5");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "   - Utilisateurs:\n";
    foreach ($users as $user) {
        echo "     * " . $user['email'] . "\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Erreur base de données: " . $e->getMessage() . "\n";
}
echo "\n";

// 3. Vérifier les routes
echo "3. Routes API:\n";
try {
    $routes = \Illuminate\Support\Facades\Route::getRoutes();
    $apiRoutes = [];
    
    foreach ($routes as $route) {
        $uri = $route->uri();
        if (strpos($uri, 'api/') === 0) {
            $apiRoutes[] = $route->methods()[0] . ' ' . $uri;
        }
    }
    
    if (empty($apiRoutes)) {
        echo "   ❌ Aucune route API trouvée\n";
    } else {
        echo "   ✅ Routes API trouvées (" . count($apiRoutes) . "):\n";
        foreach (array_slice($apiRoutes, 0, 10) as $route) {
            echo "     * $route\n";
        }
        if (count($apiRoutes) > 10) {
            echo "     ... et " . (count($apiRoutes) - 10) . " autres\n";
        }
    }
} catch (Exception $e) {
    echo "   ❌ Erreur routes: " . $e->getMessage() . "\n";
}
echo "\n";

// 4. Tester l'authentification
echo "4. Test d'authentification:\n";
try {
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    
    if (!$user) {
        echo "   ❌ Utilisateur <EMAIL> non trouvé\n";
    } else {
        echo "   ✅ Utilisateur trouvé: " . $user->name . "\n";
        
        // Tester le mot de passe
        if (\Illuminate\Support\Facades\Hash::check('password123', $user->password)) {
            echo "   ✅ Mot de passe correct\n";
        } else {
            echo "   ❌ Mot de passe incorrect\n";
        }
        
        // Tester la création de token
        try {
            $token = $user->createToken('test-token');
            echo "   ✅ Création de token OK\n";
        } catch (Exception $e) {
            echo "   ❌ Erreur création token: " . $e->getMessage() . "\n";
        }
    }
} catch (Exception $e) {
    echo "   ❌ Erreur test auth: " . $e->getMessage() . "\n";
}
echo "\n";

// 5. Vérifier les contrôleurs
echo "5. Contrôleurs:\n";
$controllers = [
    'App\Http\Controllers\Api\AuthController',
    'App\Http\Controllers\Api\ProjectController',
];

foreach ($controllers as $controller) {
    if (class_exists($controller)) {
        echo "   ✅ $controller existe\n";
    } else {
        echo "   ❌ $controller manquant\n";
    }
}
echo "\n";

// 6. Vérifier Sanctum
echo "6. Laravel Sanctum:\n";
try {
    if (class_exists('Laravel\Sanctum\Sanctum')) {
        echo "   ✅ Sanctum installé\n";
        
        // Vérifier la table personal_access_tokens
        $pdo = new PDO('sqlite:' . database_path('database.sqlite'));
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='personal_access_tokens'");
        if ($stmt->fetch()) {
            echo "   ✅ Table personal_access_tokens existe\n";
        } else {
            echo "   ❌ Table personal_access_tokens manquante\n";
        }
    } else {
        echo "   ❌ Sanctum non installé\n";
    }
} catch (Exception $e) {
    echo "   ❌ Erreur Sanctum: " . $e->getMessage() . "\n";
}
echo "\n";

echo "Diagnostic terminé.\n";
