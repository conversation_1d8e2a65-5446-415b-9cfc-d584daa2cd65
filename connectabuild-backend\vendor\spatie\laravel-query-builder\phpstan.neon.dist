includes:
    - ./vendor/larastan/larastan/extension.neon
    - phpstan-baseline.neon

parameters:

    paths:
        - src/
        - config/
        - database/

    # Level 9 is the highest level
    level: 5

    checkModelProperties: true
    checkOctaneCompatibility: true
    reportUnmatchedIgnoredErrors: false
    noUnnecessaryCollectionCall: true
    checkNullables: true
    treatPhpDocTypesAsCertain: false

    ignoreErrors:
        - '#Unsafe usage of new static#'
        - '#PHPDoc tag @var#'

#    excludePaths:
