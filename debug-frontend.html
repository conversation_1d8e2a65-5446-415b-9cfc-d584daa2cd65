<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug ConnectaBuild Frontend</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        button { padding: 10px 15px; margin: 5px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 Debug ConnectaBuild Frontend</h1>
    
    <div class="section">
        <h2>1. Test de l'API Backend</h2>
        <button onclick="testBackend()">Tester Backend</button>
        <div id="backend-result"></div>
    </div>

    <div class="section">
        <h2>2. Test de Login</h2>
        <button onclick="testLogin()">Tester Login</button>
        <div id="login-result"></div>
    </div>

    <div class="section">
        <h2>3. Test des Projets</h2>
        <button onclick="testProjects()">Tester Projets</button>
        <div id="projects-result"></div>
    </div>

    <div class="section">
        <h2>4. État du LocalStorage</h2>
        <button onclick="checkStorage()">Vérifier Storage</button>
        <div id="storage-result"></div>
    </div>

    <script>
        const API_URL = 'http://localhost:8000/api';
        let authToken = null;

        async function testBackend() {
            const result = document.getElementById('backend-result');
            try {
                const response = await fetch(`${API_URL}/test`);
                const data = await response.json();
                result.innerHTML = `<div class="success">✅ Backend accessible</div><pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                result.innerHTML = `<div class="error">❌ Erreur: ${error.message}</div>`;
            }
        }

        async function testLogin() {
            const result = document.getElementById('login-result');
            try {
                const response = await fetch(`${API_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    authToken = data.token;
                    localStorage.setItem('auth_token', authToken);
                    localStorage.setItem('user', JSON.stringify(data.user));
                    result.innerHTML = `<div class="success">✅ Login réussi</div><pre>Token: ${authToken.substring(0, 20)}...\nUser: ${data.user.name}</pre>`;
                } else {
                    result.innerHTML = `<div class="error">❌ Login échoué</div><pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                result.innerHTML = `<div class="error">❌ Erreur: ${error.message}</div>`;
            }
        }

        async function testProjects() {
            const result = document.getElementById('projects-result');
            const token = authToken || localStorage.getItem('auth_token');
            
            if (!token) {
                result.innerHTML = `<div class="error">❌ Pas de token. Faites d'abord le login.</div>`;
                return;
            }

            try {
                const response = await fetch(`${API_URL}/projects`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    result.innerHTML = `<div class="success">✅ Projets récupérés (${data.data.length})</div><pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    result.innerHTML = `<div class="error">❌ Erreur projets</div><pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                result.innerHTML = `<div class="error">❌ Erreur: ${error.message}</div>`;
            }
        }

        function checkStorage() {
            const result = document.getElementById('storage-result');
            const token = localStorage.getItem('auth_token');
            const user = localStorage.getItem('user');
            
            result.innerHTML = `
                <div>Token: ${token ? token.substring(0, 20) + '...' : 'Aucun'}</div>
                <div>User: ${user ? JSON.parse(user).name : 'Aucun'}</div>
                <pre>LocalStorage complet:\n${JSON.stringify({
                    auth_token: token,
                    user: user ? JSON.parse(user) : null
                }, null, 2)}</pre>
            `;
        }

        // Vérifier le storage au chargement
        window.onload = checkStorage;
    </script>
</body>
</html>
