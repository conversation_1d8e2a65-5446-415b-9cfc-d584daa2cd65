import axios from 'axios';

// Configuration de l'API
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

// Instance Axios configurée
export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Intercepteur pour ajouter le token d'authentification
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Intercepteur pour gérer les erreurs de réponse
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expiré ou invalide
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Types pour l'API
export interface User {
  id: number;
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
  company?: Company;
  discipline?: Discipline;
  unread_notifications_count?: number;
}

export interface Company {
  id: number;
  name: string;
  address?: string;
  phone?: string;
  email?: string;
}

export interface Discipline {
  id: number;
  name: string;
  color: string;
}

export interface Project {
  id: number;
  name: string;
  address: string;
  description?: string;
  cover_image?: string;
  status: 'draft' | 'published' | 'completed';
  created_by: number;
  creator?: User;
  levels?: Level[];
  members?: User[];
  work_areas?: WorkArea[];
  created_at: string;
  updated_at: string;
}

export interface Level {
  id: number;
  project_id: number;
  name: string;
  order_index: number;
  plans?: Plan[];
  overlays?: Overlay[];
  work_areas?: WorkArea[];
}

export interface Plan {
  id: number;
  level_id: number;
  name: string;
  file_path: string;
  file_size: number;
  file_size_human?: string;
  mime_type: string;
  uploaded_by: number;
  uploader?: User;
  created_at: string;
}

export interface Overlay {
  id: number;
  level_id: number;
  discipline_id: number;
  name: string;
  color: string;
  file_path?: string;
  created_by: number;
  creator?: User;
  discipline?: Discipline;
}

export interface WorkArea {
  id: number;
  project_id: number;
  level_id: number;
  name: string;
  type: 'floor' | 'wall' | 'ceiling';
  category: string;
  frl?: string;
  coordinates?: any;
  level?: Level;
}

export interface TeamInvitation {
  id: number;
  project_id: number;
  email: string;
  first_name: string;
  last_name: string;
  full_name?: string;
  company_name: string;
  discipline?: Discipline;
  role: 'admin' | 'manager' | 'member' | 'viewer';
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  expires_at: string;
  invited_by: number;
  inviter?: User;
}

export interface Notification {
  id: number;
  user_id: number;
  type: string;
  title: string;
  message: string;
  data?: any;
  read_at?: string;
  created_at: string;
}

// Réponses API
export interface AuthResponse {
  user: User;
  token: string;
  token_type: string;
}

export interface ApiResponse<T> {
  data?: T;
  message?: string;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T> {
  data: T[];
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  from: number;
  to: number;
}

// Utilitaires
export const getAuthToken = (): string | null => {
  return localStorage.getItem('auth_token');
};

export const setAuthToken = (token: string): void => {
  localStorage.setItem('auth_token', token);
};

export const removeAuthToken = (): void => {
  localStorage.removeItem('auth_token');
  localStorage.removeItem('user');
};

export const getCurrentUser = (): User | null => {
  const userStr = localStorage.getItem('user');
  return userStr ? JSON.parse(userStr) : null;
};

export const setCurrentUser = (user: User): void => {
  localStorage.setItem('user', JSON.stringify(user));
};

export const removeCurrentUser = (): void => {
  localStorage.removeItem('user');
};
