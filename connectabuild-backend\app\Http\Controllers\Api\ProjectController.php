<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Spatie\QueryBuilder\QueryBuilder;

class ProjectController extends Controller
{
    /**
     * Display a listing of projects
     */
    public function index(Request $request): JsonResponse
    {
        $userId = $request->user()->id;

        $projects = QueryBuilder::for(Project::class)
            ->allowedFilters(['name', 'status', 'address'])
            ->allowedSorts(['name', 'created_at', 'updated_at'])
            ->allowedIncludes(['creator', 'levels', 'members'])
            ->where(function ($query) use ($userId) {
                $query->whereHas('members', function ($subQuery) use ($userId) {
                    $subQuery->where('user_id', $userId);
                })->orWhere('created_by', $userId);
            })
            ->with(['creator:id,name,email'])
            ->paginate($request->get('per_page', 15));

        return response()->json($projects);
    }

    /**
     * Store a newly created project
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'address' => 'required|string',
            'description' => 'nullable|string',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $project = new Project([
            'name' => $request->name,
            'address' => $request->address,
            'description' => $request->description,
            'created_by' => $request->user()->id,
        ]);

        if ($request->hasFile('cover_image')) {
            $path = $request->file('cover_image')->store('project-covers', 'public');
            $project->cover_image = $path;
        }

        $project->save();

        // Add creator as admin member
        $project->members()->attach($request->user()->id, [
            'role' => 'admin',
            'joined_at' => now(),
        ]);

        return response()->json([
            'project' => $project->load(['creator', 'levels']),
            'message' => 'Project created successfully',
        ], 201);
    }

    /**
     * Display the specified project
     */
    public function show(Request $request, Project $project): JsonResponse
    {
        // Check if user has access to this project
        if (!$this->userHasAccessToProject($request->user(), $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $project->load([
            'creator:id,name,email',
            'levels.plans',
            'levels.overlays.discipline',
            'workAreas',
            'members:id,name,email,company_id',
            'members.company:id,name'
        ]);

        return response()->json(['project' => $project]);
    }

    /**
     * Update the specified project
     */
    public function update(Request $request, Project $project): JsonResponse
    {
        // Check if user has access to this project
        if (!$this->userHasAccessToProject($request->user(), $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'address' => 'sometimes|required|string',
            'description' => 'nullable|string',
            'status' => 'sometimes|in:draft,published,completed',
        ]);

        $project->update($request->only(['name', 'address', 'description', 'status']));

        return response()->json([
            'project' => $project->load(['creator', 'levels']),
            'message' => 'Project updated successfully',
        ]);
    }

    /**
     * Remove the specified project
     */
    public function destroy(Request $request, Project $project): JsonResponse
    {
        // Only creator or admin can delete
        if ($project->created_by !== $request->user()->id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Delete cover image if exists
        if ($project->cover_image) {
            Storage::disk('public')->delete($project->cover_image);
        }

        $project->delete();

        return response()->json(['message' => 'Project deleted successfully']);
    }

    /**
     * Upload cover image for project
     */
    public function uploadCoverImage(Request $request, Project $project): JsonResponse
    {
        // Check if user has access to this project
        if (!$this->userHasAccessToProject($request->user(), $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'cover_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Delete old cover image if exists
        if ($project->cover_image) {
            Storage::disk('public')->delete($project->cover_image);
        }

        $path = $request->file('cover_image')->store('project-covers', 'public');
        $project->update(['cover_image' => $path]);

        return response()->json([
            'project' => $project,
            'message' => 'Cover image uploaded successfully',
        ]);
    }

    /**
     * Check if user has access to project
     */
    private function userHasAccessToProject($user, $project): bool
    {
        return $project->created_by === $user->id || 
               $project->members()->where('user_id', $user->id)->exists();
    }
}
