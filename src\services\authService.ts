import { apiClient, AuthResponse, User, setAuthToken, setCurrentUser, removeAuthToken, removeCurrentUser } from '@/lib/api';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
  company_id?: number;
  discipline_id?: number;
  phone?: string;
}

export const authService = {
  /**
   * Connexion utilisateur
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/login', credentials);
    
    // Stocker le token et l'utilisateur
    setAuthToken(response.data.token);
    setCurrentUser(response.data.user);
    
    return response.data;
  },

  /**
   * Inscription utilisateur
   */
  async register(data: RegisterData): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/register', data);
    
    // Stocker le token et l'utilisateur
    setAuthToken(response.data.token);
    setCurrentUser(response.data.user);
    
    return response.data;
  },

  /**
   * Récupérer l'utilisateur actuel
   */
  async getCurrentUser(): Promise<User> {
    const response = await apiClient.get<{ user: User }>('/auth/user');
    
    // Mettre à jour l'utilisateur stocké
    setCurrentUser(response.data.user);
    
    return response.data.user;
  },

  /**
   * Déconnexion
   */
  async logout(): Promise<void> {
    try {
      await apiClient.post('/auth/logout');
    } catch (error) {
      // Même si l'API échoue, on supprime les données locales
      console.error('Logout API error:', error);
    } finally {
      removeAuthToken();
      removeCurrentUser();
    }
  },

  /**
   * Déconnexion de tous les appareils
   */
  async logoutAll(): Promise<void> {
    try {
      await apiClient.post('/auth/logout-all');
    } catch (error) {
      console.error('Logout all API error:', error);
    } finally {
      removeAuthToken();
      removeCurrentUser();
    }
  },

  /**
   * Vérifier si l'utilisateur est connecté
   */
  isAuthenticated(): boolean {
    return !!localStorage.getItem('auth_token');
  },

  /**
   * Récupérer l'utilisateur depuis le localStorage
   */
  getStoredUser(): User | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }
};
