import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { overlayService, CreateOverlayData, UpdateOverlayData } from '@/services/overlayService';
import { toast } from 'sonner';

export const useOverlays = (levelId: number) => {
  return useQuery({
    queryKey: ['overlays', levelId],
    queryFn: () => overlayService.getOverlays(levelId),
    enabled: !!levelId,
  });
};

export const useOverlay = (id: number) => {
  return useQuery({
    queryKey: ['overlay', id],
    queryFn: () => overlayService.getOverlay(id),
    enabled: !!id,
  });
};

export const useCreateOverlay = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ levelId, data }: { levelId: number; data: CreateOverlayData }) =>
      overlayService.createOverlay(levelId, data),
    onSuccess: (overlay) => {
      // Invalider le cache des overlays pour ce niveau
      queryClient.invalidateQueries({ queryKey: ['overlays', overlay.level_id] });
      toast.success('Overlay created successfully!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to create overlay';
      toast.error(message);
    },
  });
};

export const useUpdateOverlay = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateOverlayData }) =>
      overlayService.updateOverlay(id, data),
    onSuccess: (overlay) => {
      // Invalider le cache des overlays
      queryClient.invalidateQueries({ queryKey: ['overlays', overlay.level_id] });
      queryClient.invalidateQueries({ queryKey: ['overlay', overlay.id] });
      toast.success('Overlay updated successfully!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to update overlay';
      toast.error(message);
    },
  });
};

export const useDeleteOverlay = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => overlayService.deleteOverlay(id),
    onSuccess: (_, deletedId) => {
      // Invalider tous les caches d'overlays
      queryClient.invalidateQueries({ queryKey: ['overlays'] });
      queryClient.removeQueries({ queryKey: ['overlay', deletedId] });
      toast.success('Overlay deleted successfully!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to delete overlay';
      toast.error(message);
    },
  });
};
