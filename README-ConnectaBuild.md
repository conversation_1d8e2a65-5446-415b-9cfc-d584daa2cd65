# ConnectaBuild - Plateforme de Gestion de Projets de Construction

Une solution complète de gestion de projets de construction avec backend Laravel et frontend React. ConnectaBuild permet aux équipes de construction de collaborer efficacement sur des projets avec gestion des plans, overlays, équipes et zones de travail.

## 🏗️ Fonctionnalités

### **Backend Laravel API**
- **Authentification sécurisée** avec Laravel Sanctum
- **Gestion des projets** : CRUD complet avec statuts et images
- **Plans et overlays** : Upload et gestion par discipline
- **Équipes collaboratives** : Invitations et gestion des rôles
- **Zones de travail** : Catégorisation et actions groupées
- **Notifications** : Système de notifications en temps réel

### **Frontend React**
- **Interface moderne** : Design responsive avec branding ConnectaBuild
- **Dashboard projets** : Vue d'ensemble avec recherche et filtres
- **Création de projets** : Workflow en 4 étapes
- **Gestion d'équipe** : Invitations et permissions
- **TypeScript** : Sécurité des types et meilleure DX

## 🛠️ Stack Technique

### Backend
- **Laravel 12** - Framework PHP moderne
- **Laravel Sanctum** - Authentification API
- **Spatie Packages** - Permission, MediaLibrary, QueryBuilder
- **SQLite/MySQL** - Base de données

### Frontend
- **React 18** - Framework JavaScript moderne
- **TypeScript** - Typage statique
- **Vite** - Build tool rapide
- **Tailwind CSS** - Framework CSS utility-first
- **shadcn/ui** - Composants UI de qualité
- **React Query** - Gestion d'état et cache
- **React Router** - Navigation

## 🚀 Installation et Démarrage

### Prérequis
- **PHP 8.2+** avec Composer
- **Node.js 18+** avec npm
- **Base de données** (SQLite par défaut)

### Installation Rapide

1. **Cloner le repository**
```bash
git clone <repository-url>
cd connectabuild
```

2. **Installer le backend Laravel**
```bash
cd connectabuild-backend
composer install
cp .env.example .env
php artisan key:generate
php artisan migrate
php artisan db:seed
php artisan storage:link
```

3. **Installer le frontend React**
```bash
cd ..
npm install
```

4. **Démarrer l'environnement de développement**

**Windows :**
```bash
./start-dev.bat
```

**Linux/Mac :**
```bash
chmod +x start-dev.sh
./start-dev.sh
```

**Ou manuellement :**
```bash
# Terminal 1 - Backend
cd connectabuild-backend
php artisan serve

# Terminal 2 - Frontend  
npm run dev
```

### Accès à l'application
- **Frontend** : http://localhost:5173
- **Backend API** : http://localhost:8000/api

## 👥 Comptes de Test

Après avoir exécuté les seeders :

```
Email: <EMAIL>
Password: password123
Role: Admin ConnectaBuild

Email: <EMAIL>
Password: password123  
Role: Plombier

Email: <EMAIL>
Password: password123
Role: Électricien
```

## 📁 Structure du Projet

```
connectabuild/
├── connectabuild-backend/          # API Laravel
│   ├── app/
│   │   ├── Http/Controllers/Api/   # Contrôleurs API
│   │   ├── Models/                 # Modèles Eloquent
│   │   └── Services/               # Services métier
│   ├── database/
│   │   ├── migrations/             # Migrations DB
│   │   └── seeders/                # Données de test
│   └── routes/api.php              # Routes API
├── src/                            # Frontend React
│   ├── components/                 # Composants React
│   ├── hooks/                      # Hooks personnalisés
│   ├── services/                   # Services API
│   ├── lib/                        # Utilitaires
│   └── pages/                      # Pages
├── .env.local                      # Config frontend
└── README.md
```

## 🔧 Configuration

### Variables d'environnement

**Frontend (.env.local) :**
```env
VITE_API_URL=http://localhost:8000/api
```

**Backend (.env) :**
```env
APP_NAME="ConnectaBuild API"
APP_URL=http://localhost:8000
DB_CONNECTION=sqlite
```

## 📚 Documentation API

### Authentification
```bash
POST /api/auth/login
POST /api/auth/register
GET  /api/auth/user
POST /api/auth/logout
```

### Projets
```bash
GET    /api/projects
POST   /api/projects
GET    /api/projects/{id}
PUT    /api/projects/{id}
DELETE /api/projects/{id}
```

### Niveaux et Plans
```bash
GET  /api/projects/{project}/levels
POST /api/projects/{project}/levels
POST /api/levels/{level}/plans
```

Voir `connectabuild-backend/README-ConnectaBuild.md` pour la documentation complète.

## 🧪 Tests

```bash
# Backend
cd connectabuild-backend
php artisan test

# Frontend
npm run test
```

## 🚀 Déploiement

### Backend (Laravel)
1. Configurer la base de données de production
2. Définir `APP_ENV=production` dans `.env`
3. Exécuter `php artisan migrate --force`
4. Configurer le serveur web (Apache/Nginx)

### Frontend (React)
1. Définir `VITE_API_URL` pour la production
2. Exécuter `npm run build`
3. Déployer le dossier `dist/` sur le serveur

## 🤝 Contribution

1. Fork le repository
2. Créer une branche feature
3. Faire les modifications
4. Tester thoroughly
5. Soumettre une pull request

## 📝 Licence

Ce projet est sous licence MIT.
