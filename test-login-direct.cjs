const axios = require('axios');

async function testLoginDirect() {
  console.log('🧪 Test direct du login API...\n');

  try {
    console.log('Tentative de <NAME_EMAIL>...');
    
    const response = await axios.post('http://localhost:8000/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      validateStatus: () => true // Accepter toutes les réponses
    });

    console.log('Status:', response.status);
    console.log('Headers:', response.headers);
    console.log('Data:', JSON.stringify(response.data, null, 2));

    if (response.status === 200) {
      console.log('\n✅ LOGIN RÉUSSI !');
      console.log('Token:', response.data.token ? 'Présent' : 'Absent');
      console.log('User:', response.data.user?.name || 'Non trouvé');
    } else {
      console.log('\n❌ LOGIN ÉCHOUÉ');
      console.log('Erreur:', response.data.message || 'Pas de message');
    }

  } catch (error) {
    console.log('❌ Erreur de connexion:');
    console.log('Message:', error.message);
    console.log('Code:', error.code);
    
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Data:', error.response.data);
    }
  }
}

testLoginDirect();
