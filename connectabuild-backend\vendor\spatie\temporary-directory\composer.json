{"name": "spatie/temporary-directory", "description": "Easily create, use and destroy temporary directories", "license": "MIT", "keywords": ["spatie", "php", "temporary-directory"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "homepage": "https://github.com/spatie/temporary-directory", "require": {"php": "^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"Spatie\\TemporaryDirectory\\": "src"}}, "autoload-dev": {"psr-4": {"Spatie\\TemporaryDirectory\\Test\\": "tests"}}, "config": {"sort-packages": true}, "scripts": {"test": "vendor/bin/phpunit", "test-coverage": "vendor/bin/phpunit --coverage-html coverage"}}