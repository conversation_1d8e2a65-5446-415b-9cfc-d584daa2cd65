import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ArrowLeft, Mail } from "lucide-react";
import connectaIcon from "@/assets/connecta-logo-icon.png";
import connectaIllustration from "@/assets/connecta-illustration.png";

const ForgotPassword = () => {
  const [email, setEmail] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Simuler l'envoi d'email (à implémenter avec l'API)
    setTimeout(() => {
      setIsLoading(false);
      setIsSubmitted(true);
    }, 2000);
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen flex">
        {/* Left side - Illustration */}
        <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-connecta-navy to-connecta-dark-navy items-center justify-center p-12">
          <div className="max-w-md text-center">
            <img 
              src={connectaIllustration} 
              alt="ConnectaBuild Illustration" 
              className="w-full h-auto mb-8"
            />
            <h2 className="text-3xl font-bold text-white mb-4">
              Email Sent!
            </h2>
            <p className="text-connecta-light text-lg">
              Check your email to reset your password.
            </p>
          </div>
        </div>

        {/* Right side - Success Message */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
          <div className="w-full max-w-md space-y-8 text-center">
            <div>
              <img 
                src={connectaIcon} 
                alt="ConnectaBuild" 
                className="h-12 w-auto mx-auto mb-4"
              />
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Mail className="w-8 h-8 text-green-600" />
              </div>
              <h1 className="text-2xl font-bold text-foreground mb-2">
                Email Sent!
              </h1>
              <p className="text-muted-foreground">
                We've sent a reset link to <strong>{email}</strong>
              </p>
            </div>

            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Didn't receive the email? Check your spam folder or
              </p>
              <Button
                onClick={() => setIsSubmitted(false)}
                variant="outline"
                className="w-full"
              >
                Resend Email
              </Button>
            </div>

            <div className="text-center">
              <Link 
                to="/login" 
                className="inline-flex items-center text-connecta-navy hover:text-connecta-dark-navy font-medium"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Login
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex">
      {/* Left side - Illustration */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-connecta-navy to-connecta-dark-navy items-center justify-center p-12">
        <div className="max-w-md text-center">
          <img 
            src={connectaIllustration} 
            alt="ConnectaBuild Illustration" 
            className="w-full h-auto mb-8"
          />
          <h2 className="text-3xl font-bold text-white mb-4">
            Forgot Password?
          </h2>
          <p className="text-connecta-light text-lg">
            No problem! We'll send you a link to reset your password.
          </p>
        </div>
      </div>

      {/* Right side - Reset Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
        <div className="w-full max-w-md space-y-8">
          {/* Logo */}
          <div className="text-center">
            <img 
              src={connectaIcon} 
              alt="ConnectaBuild" 
              className="h-12 w-auto mx-auto mb-4"
            />
            <h1 className="text-2xl font-bold text-foreground">
              Reset Password
            </h1>
            <p className="text-muted-foreground mt-2">
              Enter your email to receive a reset link
            </p>
          </div>

          {/* Reset Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="mt-1"
                placeholder="<EMAIL>"
              />
            </div>

            <Button
              type="submit"
              disabled={isLoading}
              className="w-full h-12 bg-connecta-dark-navy hover:bg-connecta-navy text-white font-medium text-lg disabled:opacity-50"
            >
              {isLoading ? "Sending..." : "Send Reset Link"}
            </Button>
          </form>

          {/* Back to Login */}
          <div className="text-center">
            <Link 
              to="/login" 
              className="inline-flex items-center text-connecta-navy hover:text-connecta-dark-navy font-medium"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Login
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
