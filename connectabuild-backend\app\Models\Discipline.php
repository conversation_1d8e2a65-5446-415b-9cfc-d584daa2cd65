<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Discipline extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'color',
    ];

    /**
     * Get the users for the discipline.
     */
    public function users(): Has<PERSON><PERSON>
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the overlays for the discipline.
     */
    public function overlays(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Overlay::class);
    }

    /**
     * Get the team invitations for the discipline.
     */
    public function teamInvitations(): Has<PERSON><PERSON>
    {
        return $this->hasMany(TeamInvitation::class);
    }
}
