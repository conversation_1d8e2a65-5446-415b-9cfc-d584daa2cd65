<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Company;
use App\Models\Discipline;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $metroplumbing = Company::where('name', 'Metro Plumbing Solutions')->first();
        $sydneyElectricals = Company::where('name', 'Sydney Electricals')->first();
        $connectabuild = Company::where('name', 'ConnectaBuild Pty Ltd')->first();

        $plumbing = Discipline::where('name', 'Plumbing')->first();
        $electrical = Discipline::where('name', 'Electrical')->first();
        $architectural = Discipline::where('name', 'Architectural')->first();

        $users = [
            [
                'name' => '<PERSON>',
                'email' => 'jam<PERSON><PERSON><PERSON><EMAIL>',
                'password' => Hash::make('password123'),
                'company_id' => $metroplumbing?->id,
                'discipline_id' => $plumbing?->id,
                'phone' => '0409536295',
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'company_id' => $sydneyElectricals?->id,
                'discipline_id' => $electrical?->id,
                'phone' => '02 9876 5432',
            ],
            [
                'name' => 'Pierre Dubois',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'company_id' => $connectabuild?->id,
                'discipline_id' => $architectural?->id,
                'phone' => '0409536295',
            ],
            [
                'name' => 'Jacob Wilson',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'company_id' => $connectabuild?->id,
                'discipline_id' => $architectural?->id,
                'phone' => '02 1234 5678',
            ],
        ];

        foreach ($users as $userData) {
            User::firstOrCreate(
                ['email' => $userData['email']],
                $userData
            );
        }
    }
}
