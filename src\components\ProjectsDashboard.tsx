import { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Plus, Loader2 } from "lucide-react";
import ProjectCard from "@/components/ProjectCard";
import { useProjects } from "@/hooks/useProjects";
import { Project } from "@/lib/api";
import { projectService } from "@/services/projectService";

const ProjectsDashboard = () => {
  const [activeTab, setActiveTab] = useState<"Active" | "Archived">("Active");
  const [searchQuery, setSearchQuery] = useState("");
  const navigate = useNavigate();

  // Récupérer les projets depuis l'API
  const { data: projectsResponse, isLoading, error } = useProjects({
    name: searchQuery || undefined,
    include: ['creator'],
  });

  // DEBUG: Log des données
  console.log('ProjectsDashboard Debug:', {
    isLoading,
    error: error?.message,
    projectsResponse,
    projectsCount: projectsResponse?.data?.length,
    authToken: localStorage.getItem('auth_token') ? 'Present' : 'Missing'
  });

  // Filtrer les projets selon l'onglet actif et la recherche
  const filteredProjects = useMemo(() => {
    if (!projectsResponse?.data) return [];

    let filtered = projectsResponse.data;

    // Filtrer par statut selon l'onglet
    if (activeTab === "Active") {
      filtered = filtered.filter(project =>
        project.status === 'draft' || project.status === 'published'
      );
    } else {
      filtered = filtered.filter(project => project.status === 'completed');
    }

    // Filtrer par recherche
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(project =>
        project.name.toLowerCase().includes(query) ||
        project.address.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [projectsResponse?.data, activeTab, searchQuery]);

  const handleStatusChange = (projectId: number, newStatus: 'draft' | 'published' | 'completed') => {
    // TODO: Implémenter la mise à jour du statut via l'API
    console.log("Update project status:", projectId, newStatus);
  };

  const handleManageProject = (projectId: number) => {
    navigate(`/projects/${projectId}`);
  };

  const handleCreateNew = () => {
    navigate("/projects/create");
  };

  // Fonction pour formater la date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  };

  // Fonction pour obtenir l'URL de l'image de couverture
  const getCoverImageUrl = (coverImage?: string) => {
    return projectService.getCoverImageUrl(coverImage);
  };

  return (
    <div className="flex-1 p-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold text-foreground">Projects</h1>
        <Button onClick={handleCreateNew} className="btn-primary flex items-center gap-2">
          <Plus className="w-4 h-4" />
          Create New
        </Button>
      </div>

      {/* Tabs */}
      <div className="tab-nav mb-6">
        <button
          onClick={() => setActiveTab("Active")}
          className={`tab-item ${activeTab === "Active" ? "active" : ""}`}
        >
          Active
        </button>
        <button
          onClick={() => setActiveTab("Archived")}
          className={`tab-item ${activeTab === "Archived" ? "active" : ""}`}
        >
          Archived
        </button>
      </div>

      {/* Search */}
      <div className="relative mb-6">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
        <Input
          placeholder="Search for a project..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="search-input pl-10"
        />
      </div>

      {/* Projects List */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-connecta-navy" />
            <span className="ml-2 text-muted-foreground">Chargement des projets...</span>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <p className="text-red-500 mb-4">Erreur lors du chargement des projets</p>
            <Button onClick={() => window.location.reload()} variant="outline">
              Réessayer
            </Button>
          </div>
        ) : filteredProjects.length > 0 ? (
          filteredProjects.map((project) => (
            <ProjectCard
              key={project.id}
              project={{
                id: project.id.toString(),
                name: project.name,
                address: project.address,
                lastUpdated: formatDate(project.updated_at),
                status: project.status === 'draft' ? 'Draft' :
                       project.status === 'published' ? 'Published' : 'Completed',
                image: getCoverImageUrl(project.cover_image),
              }}
              onStatusChange={(id, status) => handleStatusChange(parseInt(id), status as any)}
              onManage={(id) => handleManageProject(parseInt(id))}
            />
          ))
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground">No projects found matching your search.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectsDashboard;