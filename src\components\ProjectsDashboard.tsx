import { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Plus, Loader2 } from "lucide-react";
import ProjectCard from "@/components/ProjectCard";
import { useProjects } from "@/hooks/useProjects";
import { Project } from "@/lib/api";
import { projectService } from "@/services/projectService";

const ProjectsDashboard = () => {
  const [activeTab, setActiveTab] = useState<"Active" | "Archived">("Active");
  const [searchQuery, setSearchQuery] = useState("");
  const navigate = useNavigate();

  // Récupérer les projets depuis l'API
  const { data: projectsResponse, isLoading, error } = useProjects({
    name: searchQuery || undefined,
    include: ['creator'],
  });

  // DEBUG: Log data
  console.log('ProjectsDashboard Debug:', {
    isLoading,
    error: error?.message,
    projectsResponse,
    projectsCount: projectsResponse?.data?.length,
    authToken: localStorage.getItem('auth_token') ? 'Present' : 'Missing',
    apiUrl: import.meta.env.VITE_API_URL
  });

  // DEBUG: Fonction globale pour tester la mise à jour
  (window as any).testUpdateProject = async (projectId: number, frontendStatus: string) => {
    try {
      const backendStatus = mapStatusToBackend(frontendStatus);
      console.log('Testing project update:', { projectId, frontendStatus, backendStatus });
      const result = await projectService.updateProject(projectId, { status: backendStatus });
      console.log('Update successful:', result);
      return result;
    } catch (error) {
      console.error('Update failed:', error);
      throw error;
    }
  };

  // Filtrer les projets selon l'onglet actif et la recherche
  const filteredProjects = useMemo(() => {
    if (!projectsResponse?.data) return [];

    let filtered = projectsResponse.data;

    // Filtrer par statut selon l'onglet
    if (activeTab === "Active") {
      filtered = filtered.filter(project =>
        project.status === 'draft' || project.status === 'published'
      );
    } else {
      filtered = filtered.filter(project => project.status === 'completed');
    }

    // Filtrer par recherche
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(project =>
        project.name.toLowerCase().includes(query) ||
        project.address.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [projectsResponse?.data, activeTab, searchQuery]);

  // Fonction pour mapper les statuts frontend vers backend
  const mapStatusToBackend = (frontendStatus: string): 'draft' | 'published' | 'completed' => {
    switch (frontendStatus) {
      case 'Draft':
        return 'draft';
      case 'Published':
        return 'published';
      case 'Completed':
        return 'completed';
      default:
        return frontendStatus.toLowerCase() as 'draft' | 'published' | 'completed';
    }
  };

  // Fonction pour mapper les statuts backend vers frontend
  const mapStatusToFrontend = (backendStatus: string): 'Draft' | 'Published' | 'Completed' => {
    switch (backendStatus) {
      case 'draft':
        return 'Draft';
      case 'published':
        return 'Published';
      case 'completed':
        return 'Completed';
      default:
        return backendStatus.charAt(0).toUpperCase() + backendStatus.slice(1) as 'Draft' | 'Published' | 'Completed';
    }
  };

  const handleStatusChange = async (projectId: number, frontendStatus: string) => {
    const backendStatus = mapStatusToBackend(frontendStatus);
    try {
      console.log('Updating project status:', { projectId, frontendStatus, backendStatus });

      // Vérifier que l'utilisateur est authentifié
      const token = localStorage.getItem('auth_token');
      if (!token) {
        alert('You are not authenticated. Please log in again.');
        navigate('/login');
        return;
      }

      // Utiliser le service pour mettre à jour le statut
      await projectService.updateProject(projectId, { status: backendStatus });

      console.log('Project status updated successfully');

      // Recharger les projets pour refléter le changement
      window.location.reload();
    } catch (error: any) {
      console.error('Error updating project status:', error);

      // Gestion d'erreurs plus détaillée
      let errorMessage = 'Failed to update project status. Please try again.';

      if (error.response) {
        const status = error.response.status;
        const data = error.response.data;

        if (status === 401) {
          errorMessage = 'You are not authorized. Please log in again.';
          navigate('/login');
          return;
        } else if (status === 403) {
          errorMessage = 'You do not have permission to update this project.';
        } else if (status === 422) {
          errorMessage = `Invalid data: ${data.message || 'Please check your input.'}`;
        } else if (data.message) {
          errorMessage = data.message;
        }
      }

      alert(errorMessage);
    }
  };

  const handleManageProject = (projectId: number) => {
    navigate(`/projects/${projectId}`);
  };

  const handleCreateNew = () => {
    navigate("/projects/create");
  };

  // Fonction pour formater la date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  };

  // Fonction pour obtenir l'URL de l'image de couverture
  const getCoverImageUrl = (coverImage?: string) => {
    return projectService.getCoverImageUrl(coverImage);
  };

  return (
    <div className="flex-1 p-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold text-foreground">Projects</h1>
        <Button onClick={handleCreateNew} className="btn-primary flex items-center gap-2">
          <Plus className="w-4 h-4" />
          Create New
        </Button>
      </div>

      {/* Tabs */}
      <div className="tab-nav mb-6">
        <button
          onClick={() => setActiveTab("Active")}
          className={`tab-item ${activeTab === "Active" ? "active" : ""}`}
        >
          Active
        </button>
        <button
          onClick={() => setActiveTab("Archived")}
          className={`tab-item ${activeTab === "Archived" ? "active" : ""}`}
        >
          Archived
        </button>
      </div>

      {/* Search */}
      <div className="relative mb-6">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
        <Input
          placeholder="Search for a project..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="search-input pl-10"
        />
      </div>

      {/* Projects List */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-connecta-navy" />
            <span className="ml-2 text-muted-foreground">Loading projects...</span>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <p className="text-red-500 mb-4">Error loading projects</p>
            <Button onClick={() => window.location.reload()} variant="outline">
              Retry
            </Button>
          </div>
        ) : filteredProjects.length > 0 ? (
          filteredProjects.map((project) => (
            <ProjectCard
              key={project.id}
              project={{
                id: project.id.toString(),
                name: project.name,
                address: project.address,
                lastUpdated: formatDate(project.updated_at),
                status: mapStatusToFrontend(project.status),
                image: getCoverImageUrl(project.cover_image),
              }}
              onStatusChange={(id, status) => handleStatusChange(parseInt(id), status)}
              onManage={(id) => handleManageProject(parseInt(id))}
            />
          ))
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground">No projects found matching your search.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectsDashboard;