import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Plus } from "lucide-react";
import ProjectCard from "@/components/ProjectCard";

type Status = "Draft" | "Published" | "Completed";

interface Project {
  id: string;
  name: string;
  address: string;
  lastUpdated: string;
  status: Status;
  image?: string;
}

const ProjectsDashboard = () => {
  const [activeTab, setActiveTab] = useState<"Active" | "Archived">("Active");
  const [searchQuery, setSearchQuery] = useState("");
  const [projects, setProjects] = useState<Project[]>([
    {
      id: "1",
      name: "Dee Why Suites",
      address: "8 Oaks Avenue, Dee Why, 2099 NSW",
      lastUpdated: "01 Jul 2024",
      status: "Draft",
    },
    {
      id: "2",
      name: "Chatswood Mall Expansion",
      address: "17 Victoria Avenue, Chatswood, NSW 2067",
      lastUpdated: "01 Jul 2024",
      status: "Published",
      image: "https://images.unsplash.com/photo-1486718448742-163732cd1544?w=64&h=64&fit=crop&crop=center",
    },
    {
      id: "3",
      name: "Parramatta Business Park",
      address: "15 Smith Street, Parramatta, NSW 2150",
      lastUpdated: "01 Jul 2024",
      status: "Published",
      image: "https://images.unsplash.com/photo-1497604401993-f2e922e5cb0a?w=64&h=64&fit=crop&crop=center",
    },
    {
      id: "4",
      name: "The Bondi Residences",
      address: "100 Campbell Parade, Bondi Beach, NSW 2026",
      lastUpdated: "01 Jul 2024",
      status: "Published",
      image: "https://images.unsplash.com/photo-1449157291145-7efd050a4d0e?w=64&h=64&fit=crop&crop=center",
    },
    {
      id: "5",
      name: "East Sydney Apartments",
      address: "28 George Street, Darlinghurst, NSW 2010",
      lastUpdated: "01 Jul 2024",
      status: "Published",
      image: "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=64&h=64&fit=crop&crop=center",
    },
    {
      id: "6",
      name: "Redfern Tower",
      address: "45-47 Walker Street, Redfern, NSW 2016",
      lastUpdated: "01 Jul 2024",
      status: "Published",
      image: "https://images.unsplash.com/photo-1518005020951-eccb494ad742?w=64&h=64&fit=crop&crop=center",
    },
    {
      id: "7",
      name: "Glebe Waterfront Development",
      address: "45 Bridge Road, Glebe, NSW 2037",
      lastUpdated: "01 Jul 2024",
      status: "Completed",
      image: "https://images.unsplash.com/photo-1493397212122-2b85dda8106b?w=64&h=64&fit=crop&crop=center",
    },
  ]);

  const filteredProjects = projects.filter(project =>
    project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    project.address.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleStatusChange = (projectId: string, newStatus: Status) => {
    setProjects(prev =>
      prev.map(project =>
        project.id === projectId ? { ...project, status: newStatus } : project
      )
    );
  };

  const handleManageProject = (projectId: string) => {
    console.log("Manage project:", projectId);
    // Navigate to project management page
  };

  const handleCreateNew = () => {
    console.log("Create new project");
    // Navigate to create project page
    window.location.href = "/projects/create";
  };

  return (
    <div className="flex-1 p-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold text-foreground">Projects</h1>
        <Button onClick={handleCreateNew} className="btn-primary flex items-center gap-2">
          <Plus className="w-4 h-4" />
          Create New
        </Button>
      </div>

      {/* Tabs */}
      <div className="tab-nav mb-6">
        <button
          onClick={() => setActiveTab("Active")}
          className={`tab-item ${activeTab === "Active" ? "active" : ""}`}
        >
          Active
        </button>
        <button
          onClick={() => setActiveTab("Archived")}
          className={`tab-item ${activeTab === "Archived" ? "active" : ""}`}
        >
          Archived
        </button>
      </div>

      {/* Search */}
      <div className="relative mb-6">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
        <Input
          placeholder="Search for a project..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="search-input pl-10"
        />
      </div>

      {/* Projects List */}
      <div className="space-y-4">
        {filteredProjects.length > 0 ? (
          filteredProjects.map((project) => (
            <ProjectCard
              key={project.id}
              project={project}
              onStatusChange={handleStatusChange}
              onManage={handleManageProject}
            />
          ))
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground">No projects found matching your search.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectsDashboard;