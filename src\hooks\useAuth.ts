import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { authService, LoginCredentials, RegisterData } from '@/services/authService';
import { User } from '@/lib/api';
import { toast } from 'sonner';

export const useAuth = () => {
  const queryClient = useQueryClient();

  // Query pour récupérer l'utilisateur actuel
  const userQuery = useQuery({
    queryKey: ['auth', 'user'],
    queryFn: authService.getCurrentUser,
    enabled: authService.isAuthenticated(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: false,
  });

  // Mutation pour la connexion
  const loginMutation = useMutation({
    mutationFn: authService.login,
    onSuccess: (data) => {
      queryClient.setQueryData(['auth', 'user'], data.user);
      toast.success('Login successful!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Login error';
      toast.error(message);
    },
  });

  // Mutation pour l'inscription
  const registerMutation = useMutation({
    mutationFn: authService.register,
    onSuccess: (data) => {
      queryClient.setQueryData(['auth', 'user'], data.user);
      toast.success('Registration successful!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Registration error';
      toast.error(message);
    },
  });

  // Mutation pour la déconnexion
  const logoutMutation = useMutation({
    mutationFn: authService.logout,
    onSuccess: () => {
      queryClient.clear();
      toast.success('Logout successful');
    },
    onError: (error: any) => {
      console.error('Logout error:', error);
      // Même en cas d'erreur, on nettoie le cache local
      queryClient.clear();
    },
  });

  return {
    // État
    user: userQuery.data,
    isLoading: userQuery.isLoading,
    isAuthenticated: authService.isAuthenticated(),
    
    // Actions
    login: loginMutation.mutate,
    register: registerMutation.mutate,
    logout: logoutMutation.mutate,
    
    // États des mutations
    isLoggingIn: loginMutation.isPending,
    isRegistering: registerMutation.isPending,
    isLoggingOut: logoutMutation.isPending,
    
    // Erreurs
    loginError: loginMutation.error,
    registerError: registerMutation.error,
    
    // Utilitaires
    refetchUser: userQuery.refetch,
  };
};

// Hook pour récupérer l'utilisateur depuis le localStorage (synchrone)
export const useStoredUser = (): User | null => {
  return authService.getStoredUser();
};
