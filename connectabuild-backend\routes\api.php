<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\ProjectController;
use App\Http\Controllers\Api\LevelController;
use App\Http\Controllers\Api\PlanController;
use App\Http\Controllers\Api\WorkAreaController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Test route
Route::get('test', function () {
    return response()->json(['message' => 'API ConnectaBuild fonctionne !', 'time' => now()]);
});

// Public routes
Route::prefix('auth')->group(function () {
    Route::post('login', [AuthController::class, 'login']);
    Route::post('register', [AuthController::class, 'register']);
});

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes
    Route::prefix('auth')->group(function () {
        Route::get('user', [AuthController::class, 'user']);
        Route::post('logout', [AuthController::class, 'logout']);
        Route::post('logout-all', [AuthController::class, 'logoutAll']);
    });

    // Projects
    Route::apiResource('projects', ProjectController::class);
    Route::post('projects/{project}/cover-image', [ProjectController::class, 'uploadCoverImage']);

    // Levels
    Route::get('projects/{project}/levels', [LevelController::class, 'index']);
    Route::post('projects/{project}/levels', [LevelController::class, 'store']);
    Route::post('projects/{project}/levels/reorder', [LevelController::class, 'reorder']);
    Route::get('levels/{level}', [LevelController::class, 'show']);
    Route::put('levels/{level}', [LevelController::class, 'update']);
    Route::delete('levels/{level}', [LevelController::class, 'destroy']);

    // Plans
    Route::get('levels/{level}/plans', [PlanController::class, 'index']);
    Route::post('levels/{level}/plans', [PlanController::class, 'store']);
    Route::get('plans/{plan}', [PlanController::class, 'show']);
    Route::delete('plans/{plan}', [PlanController::class, 'destroy']);
    Route::get('plans/{plan}/download', [PlanController::class, 'download']);

    // Work Areas
    Route::get('projects/{project}/work-areas', [WorkAreaController::class, 'index']);
    Route::post('projects/{project}/work-areas', [WorkAreaController::class, 'store']);
    Route::get('work-areas/{workArea}', [WorkAreaController::class, 'show']);
    Route::put('work-areas/{workArea}', [WorkAreaController::class, 'update']);
    Route::delete('work-areas/{workArea}', [WorkAreaController::class, 'destroy']);
    Route::post('work-areas/bulk-action', [WorkAreaController::class, 'bulkAction']);

    // Team Management
    // Route::get('projects/{project}/members', [TeamController::class, 'index']);
    // Route::post('projects/{project}/invite', [TeamController::class, 'invite']);
    // Route::put('project-members/{projectMember}', [TeamController::class, 'updateMember']);
    // Route::delete('project-members/{projectMember}', [TeamController::class, 'removeMember']);

    // Invitations
    // Route::get('projects/{project}/invitations', [InvitationController::class, 'index']);
    // Route::post('invitations/{token}/accept', [InvitationController::class, 'accept']);
    // Route::post('invitations/{token}/decline', [InvitationController::class, 'decline']);

    // Notifications
    // Route::get('notifications', [NotificationController::class, 'index']);
    // Route::put('notifications/{notification}/read', [NotificationController::class, 'markAsRead']);
    // Route::post('notifications/mark-all-read', [NotificationController::class, 'markAllAsRead']);

    // Companies and Disciplines (for dropdowns)
    // Route::get('companies', [CompanyController::class, 'index']);
    // Route::get('disciplines', [DisciplineController::class, 'index']);
});
