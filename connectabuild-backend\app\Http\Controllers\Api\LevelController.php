<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Project;
use App\Models\Level;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class LevelController extends Controller
{
    /**
     * Display levels for a project
     */
    public function index(Request $request, Project $project): JsonResponse
    {
        // Check if user has access to this project
        if (!$this->userHasAccessToProject($request->user(), $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $levels = $project->levels()
            ->with(['plans', 'overlays.discipline'])
            ->orderBy('order_index')
            ->get();

        return response()->json(['levels' => $levels]);
    }

    /**
     * Store a newly created level
     */
    public function store(Request $request, Project $project): JsonResponse
    {
        // Check if user has access to this project
        if (!$this->userHasAccessToProject($request->user(), $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'order_index' => 'nullable|integer',
        ]);

        $orderIndex = $request->order_index ?? $project->levels()->max('order_index') + 1;

        $level = $project->levels()->create([
            'name' => $request->name,
            'order_index' => $orderIndex,
        ]);

        return response()->json([
            'level' => $level,
            'message' => 'Level created successfully',
        ], 201);
    }

    /**
     * Display the specified level
     */
    public function show(Request $request, Level $level): JsonResponse
    {
        // Check if user has access to this project
        if (!$this->userHasAccessToProject($request->user(), $level->project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $level->load(['plans', 'overlays.discipline', 'workAreas']);

        return response()->json(['level' => $level]);
    }

    /**
     * Update the specified level
     */
    public function update(Request $request, Level $level): JsonResponse
    {
        // Check if user has access to this project
        if (!$this->userHasAccessToProject($request->user(), $level->project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'order_index' => 'sometimes|required|integer',
        ]);

        $level->update($request->only(['name', 'order_index']));

        return response()->json([
            'level' => $level,
            'message' => 'Level updated successfully',
        ]);
    }

    /**
     * Remove the specified level
     */
    public function destroy(Request $request, Level $level): JsonResponse
    {
        // Check if user has access to this project
        if (!$this->userHasAccessToProject($request->user(), $level->project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $level->delete();

        return response()->json(['message' => 'Level deleted successfully']);
    }

    /**
     * Reorder levels
     */
    public function reorder(Request $request, Project $project): JsonResponse
    {
        // Check if user has access to this project
        if (!$this->userHasAccessToProject($request->user(), $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'levels' => 'required|array',
            'levels.*.id' => 'required|exists:levels,id',
            'levels.*.order_index' => 'required|integer',
        ]);

        foreach ($request->levels as $levelData) {
            Level::where('id', $levelData['id'])
                ->where('project_id', $project->id)
                ->update(['order_index' => $levelData['order_index']]);
        }

        $levels = $project->levels()->orderBy('order_index')->get();

        return response()->json([
            'levels' => $levels,
            'message' => 'Levels reordered successfully',
        ]);
    }

    /**
     * Check if user has access to project
     */
    private function userHasAccessToProject($user, $project): bool
    {
        return $project->created_by === $user->id || 
               $project->members()->where('user_id', $user->id)->exists();
    }
}
