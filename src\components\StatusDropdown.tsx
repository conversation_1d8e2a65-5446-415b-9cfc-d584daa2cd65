import { useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";

type Status = "Draft" | "Published" | "Completed";

interface StatusDropdownProps {
  currentStatus: Status;
  onStatusChange?: (status: Status) => void;
}

const StatusDropdown = ({ currentStatus, onStatusChange }: StatusDropdownProps) => {
  const [status, setStatus] = useState<Status>(currentStatus);

  const handleStatusChange = (newStatus: Status) => {
    setStatus(newStatus);
    onStatusChange?.(newStatus);
  };

  const getStatusClass = (statusType: Status) => {
    switch (statusType) {
      case "Draft":
        return "status-draft";
      case "Published":
        return "status-published";
      case "Completed":
        return "status-completed";
      default:
        return "status-draft";
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className={cn(
          "flex items-center gap-2 min-w-[100px] justify-between",
          getStatusClass(status)
        )}>
          <span>{status}</span>
          <ChevronDown className="w-4 h-4" />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent 
        align="end" 
        className="w-32 bg-white border border-border shadow-lg z-[var(--z-dropdown)]"
      >
        <DropdownMenuItem 
          onClick={() => handleStatusChange("Draft")}
          className="cursor-pointer hover:bg-yellow-50 focus:bg-yellow-50 data-[highlighted]:bg-yellow-50"
        >
          <span className="status-draft w-full text-center">Draft</span>
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => handleStatusChange("Published")}
          className="cursor-pointer hover:bg-green-50 focus:bg-green-50 data-[highlighted]:bg-green-50"
        >
          <span className="status-published w-full text-center">Published</span>
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => handleStatusChange("Completed")}
          className="cursor-pointer hover:bg-purple-50 focus:bg-purple-50 data-[highlighted]:bg-purple-50"
        >
          <span className="status-completed w-full text-center">Completed</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default StatusDropdown;