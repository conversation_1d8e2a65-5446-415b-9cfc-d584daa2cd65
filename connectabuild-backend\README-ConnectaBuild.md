# ConnectaBuild Backend API

Backend API pour la plateforme ConnectaBuild - Une solution complète de gestion de projets de construction.

## 🏗️ À propos

ConnectaBuild est une plateforme collaborative qui permet de gérer des projets de construction de bout en bout. Ce backend fournit une API REST complète pour :

- **Gestion des projets** : Création, modification, suivi des projets de construction
- **Plans et overlays** : Upload et gestion des plans architecturaux avec overlays par discipline
- **Équipes collaboratives** : Invitation et gestion des membres d'équipe avec rôles et permissions
- **Zones de travail** : Définition et catégorisation des zones de travail
- **Notifications** : Système de notifications en temps réel

## 🛠️ Stack Technique

- **Laravel 12** - Framework PHP moderne
- **Laravel Sanctum** - Authentification API
- **Spatie <PERSON> Permission** - Gestion des rôles et permissions
- **<PERSON><PERSON> MediaLibrary** - Gestion des fichiers
- **<PERSON><PERSON> Query Builder** - API filtering et sorting
- **SQLite** - Base de données (configurable pour MySQL/PostgreSQL)

## 🚀 Installation

### Prérequis
- PHP 8.2+
- Composer
- Node.js (pour Vite)

### Étapes d'installation

1. **Cloner le repository**
```bash
git clone <repository-url>
cd connectabuild-backend
```

2. **Installer les dépendances**
```bash
composer install
```

3. **Configuration de l'environnement**
```bash
cp .env.example .env
php artisan key:generate
```

4. **Configurer la base de données**
```bash
# La configuration par défaut utilise SQLite
php artisan migrate
```

5. **Peupler la base de données**
```bash
php artisan db:seed
```

6. **Créer le lien symbolique pour le stockage**
```bash
php artisan storage:link
```

7. **Démarrer le serveur de développement**
```bash
php artisan serve
```

L'API sera accessible sur `http://localhost:8000`

## 📚 Documentation API

### Authentification

#### POST `/api/auth/login`
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### POST `/api/auth/register`
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123",
  "company_id": 1,
  "discipline_id": 1
}
```

### Projets

#### GET `/api/projects`
Liste des projets avec filtrage et pagination

#### POST `/api/projects`
```json
{
  "name": "Nouveau Projet",
  "address": "123 Rue Example, Sydney NSW",
  "description": "Description du projet"
}
```

#### GET `/api/projects/{id}`
Détails d'un projet avec niveaux, plans et équipe

### Niveaux

#### GET `/api/projects/{project}/levels`
Liste des niveaux d'un projet

#### POST `/api/projects/{project}/levels`
```json
{
  "name": "Level 2",
  "order_index": 3
}
```

## 🗄️ Structure de la Base de Données

### Entités principales
- **Users** - Utilisateurs avec entreprise et discipline
- **Projects** - Projets de construction
- **Levels** - Niveaux des projets
- **Plans** - Plans architecturaux
- **Overlays** - Overlays par discipline
- **WorkAreas** - Zones de travail
- **ProjectMembers** - Membres des projets avec rôles
- **TeamInvitations** - Invitations d'équipe
- **Notifications** - Notifications utilisateur

## 👥 Utilisateurs de test

Après avoir exécuté les seeders, vous pouvez utiliser ces comptes :

```
Email: <EMAIL>
Password: password123
Role: Admin ConnectaBuild

Email: <EMAIL>
Password: password123
Role: Plombier

Email: <EMAIL>
Password: password123
Role: Électricien
```

## 🔧 Configuration

### CORS
Le CORS est configuré pour accepter toutes les origines en développement. Pour la production, modifiez `config/cors.php`.

### Stockage des fichiers
Les fichiers sont stockés dans `storage/app/public`. Assurez-vous que le lien symbolique est créé.

### Base de données
Par défaut, SQLite est utilisé. Pour MySQL/PostgreSQL, modifiez le `.env` :

```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=connectabuild
DB_USERNAME=root
DB_PASSWORD=
```

## 🧪 Tests

```bash
php artisan test
```

## 📝 Licence

Ce projet est sous licence MIT.
