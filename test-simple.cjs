const axios = require('axios');

async function testSimple() {
  console.log('🧪 Test simple de l\'API...\n');

  try {
    // Test de la route de test
    console.log('Test de la route /api/test...');
    const response = await axios.get('http://localhost:8000/api/test');
    
    console.log('✅ Succès !');
    console.log('Réponse:', response.data);
    
  } catch (error) {
    console.log('❌ Erreur:');
    console.log('Status:', error.response?.status);
    console.log('Message:', error.message);
    console.log('Data:', error.response?.data);
  }
}

testSimple();
