import { apiClient } from '@/lib/api';

export interface Overlay {
  id: number;
  level_id: number;
  discipline_id: number;
  name: string;
  color?: string;
  file_path: string;
  created_by: number;
  created_at: string;
  updated_at: string;
  discipline?: {
    id: number;
    name: string;
    color: string;
  };
  creator?: {
    id: number;
    name: string;
    email: string;
  };
  level?: {
    id: number;
    name: string;
  };
}

export interface CreateOverlayData {
  name: string;
  discipline_id: number;
  color?: string;
  file: File;
}

export interface UpdateOverlayData {
  name?: string;
  discipline_id?: number;
  color?: string;
  file?: File;
}

export const overlayService = {
  /**
   * Récupérer tous les overlays d'un niveau
   */
  async getOverlays(levelId: number): Promise<Overlay[]> {
    const response = await apiClient.get<{ overlays: Overlay[] }>(`/levels/${levelId}/overlays`);
    return response.data.overlays;
  },

  /**
   * <PERSON><PERSON>cup<PERSON>rer un overlay par ID
   */
  async getOverlay(id: number): Promise<Overlay> {
    const response = await apiClient.get<{ overlay: Overlay }>(`/overlays/${id}`);
    return response.data.overlay;
  },

  /**
   * Créer un nouvel overlay
   */
  async createOverlay(levelId: number, data: CreateOverlayData): Promise<Overlay> {
    const formData = new FormData();
    formData.append('name', data.name);
    formData.append('discipline_id', data.discipline_id.toString());
    formData.append('file', data.file);
    
    if (data.color) {
      formData.append('color', data.color);
    }

    const response = await apiClient.post<{ overlay: Overlay; message: string }>(
      `/levels/${levelId}/overlays`, 
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    
    return response.data.overlay;
  },

  /**
   * Mettre à jour un overlay
   */
  async updateOverlay(id: number, data: UpdateOverlayData): Promise<Overlay> {
    const formData = new FormData();
    
    if (data.name) {
      formData.append('name', data.name);
    }
    
    if (data.discipline_id) {
      formData.append('discipline_id', data.discipline_id.toString());
    }
    
    if (data.color) {
      formData.append('color', data.color);
    }
    
    if (data.file) {
      formData.append('file', data.file);
    }

    const response = await apiClient.post<{ overlay: Overlay; message: string }>(
      `/overlays/${id}?_method=PUT`, 
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    
    return response.data.overlay;
  },

  /**
   * Supprimer un overlay
   */
  async deleteOverlay(id: number): Promise<void> {
    await apiClient.delete(`/overlays/${id}`);
  },

  /**
   * Télécharger un overlay
   */
  async downloadOverlay(id: number): Promise<Blob> {
    const response = await apiClient.get(`/overlays/${id}/download`, {
      responseType: 'blob',
    });
    return response.data;
  },

  /**
   * Récupérer l'URL complète du fichier overlay
   */
  getOverlayFileUrl(filePath: string | undefined): string | undefined {
    if (!filePath) return undefined;
    
    // Si c'est déjà une URL complète, la retourner
    if (filePath.startsWith('http')) {
      return filePath;
    }
    
    // Sinon, construire l'URL avec le base URL du storage
    const baseUrl = import.meta.env.VITE_API_URL?.replace('/api', '') || 'http://localhost:8000';
    return `${baseUrl}/storage/${filePath}`;
  }
};
