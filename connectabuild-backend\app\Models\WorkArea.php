<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WorkArea extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'level_id',
        'name',
        'type',
        'category',
        'frl',
        'coordinates',
    ];

    protected $casts = [
        'coordinates' => 'array',
        'type' => 'string',
    ];

    /**
     * Get the project that owns the work area.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the level that owns the work area.
     */
    public function level(): BelongsTo
    {
        return $this->belongsTo(Level::class);
    }
}
