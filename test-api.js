// Test script pour vérifier l'API ConnectaBuild
const axios = require('axios');

async function testAPI() {
  console.log('🧪 Test de l\'API ConnectaBuild...\n');

  try {
    // Test 1: Vérifier que le serveur répond
    console.log('1. Test de connexion au serveur...');
    const healthCheck = await axios.get('http://localhost:8000/api/auth/user', {
      validateStatus: () => true // Accepter toutes les réponses
    });
    
    if (healthCheck.status === 401) {
      console.log('✅ Serveur accessible (401 attendu sans token)');
    } else {
      console.log(`⚠️  Réponse inattendue: ${healthCheck.status}`);
    }

    // Test 2: Test de login avec les bonnes credentials
    console.log('\n2. Test de <NAME_EMAIL>...');
    const loginResponse = await axios.post('http://localhost:8000/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    }, {
      validateStatus: () => true
    });

    if (loginResponse.status === 200) {
      console.log('✅ Login réussi !');
      console.log('Token reçu:', loginResponse.data.token ? 'Oui' : 'Non');
      console.log('Utilisateur:', loginResponse.data.user?.name || 'Non trouvé');
    } else {
      console.log('❌ Échec du login');
      console.log('Status:', loginResponse.status);
      console.log('Erreur:', loginResponse.data);
    }

    // Test 3: Test avec de mauvaises credentials
    console.log('\n3. Test avec de mauvaises credentials...');
    const badLoginResponse = await axios.post('http://localhost:8000/api/auth/login', {
      email: '<EMAIL>',
      password: 'wrongpassword'
    }, {
      validateStatus: () => true
    });

    if (badLoginResponse.status === 422 || badLoginResponse.status === 401) {
      console.log('✅ Rejet des mauvaises credentials (attendu)');
    } else {
      console.log('⚠️  Réponse inattendue pour mauvaises credentials');
    }

  } catch (error) {
    console.log('❌ Erreur de connexion au serveur:');
    console.log('Message:', error.message);
    console.log('Code:', error.code);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Solution: Démarrez le serveur Laravel avec:');
      console.log('   cd connectabuild-backend');
      console.log('   php artisan serve');
    }
  }
}

testAPI();
