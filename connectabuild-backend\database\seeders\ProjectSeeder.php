<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Project;
use App\Models\Level;
use App\Models\WorkArea;

class ProjectSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $pierre = User::where('email', '<EMAIL>')->first();
        $jacob = User::where('email', '<EMAIL>')->first();

        if (!$pierre || !$jacob) {
            return;
        }

        $projects = [
            [
                'name' => 'Dee Why Suites',
                'address' => '8 Oaks Avenue, Dee Why, 2099 NSW',
                'description' => 'Luxury residential development with modern amenities',
                'status' => 'draft',
                'created_by' => $pierre->id,
            ],
            [
                'name' => 'Chatswood Mall Expansion',
                'address' => '17 Victoria Avenue, Chatswood, NSW 2067',
                'description' => 'Commercial expansion project for retail spaces',
                'status' => 'published',
                'created_by' => $jacob->id,
            ],
            [
                'name' => 'Parramatta Business Park',
                'address' => '15 Smith Street, Parramatta, NSW 2150',
                'description' => 'Modern office complex with sustainable design',
                'status' => 'published',
                'created_by' => $pierre->id,
            ],
            [
                'name' => 'The Bondi Residences',
                'address' => '100 Campbell Parade, Bondi Beach, NSW 2026',
                'description' => 'Beachfront residential towers with ocean views',
                'status' => 'published',
                'created_by' => $jacob->id,
            ],
            [
                'name' => 'East Sydney Apartments',
                'address' => '28 George Street, Darlinghurst, NSW 2010',
                'description' => 'Urban living apartments in the heart of Sydney',
                'status' => 'published',
                'created_by' => $pierre->id,
            ],
            [
                'name' => 'Redfern Tower',
                'address' => '45-47 Walker Street, Redfern, NSW 2016',
                'description' => 'Mixed-use development with retail and residential',
                'status' => 'published',
                'created_by' => $jacob->id,
            ],
            [
                'name' => 'Glebe Waterfront Development',
                'address' => '45 Bridge Road, Glebe, NSW 2037',
                'description' => 'Waterfront development with marina access',
                'status' => 'completed',
                'created_by' => $pierre->id,
            ],
        ];

        foreach ($projects as $projectData) {
            $project = Project::firstOrCreate(
                ['name' => $projectData['name']],
                $projectData
            );

            // Add creator as admin member
            $project->members()->syncWithoutDetaching([
                $projectData['created_by'] => [
                    'role' => 'admin',
                    'joined_at' => now(),
                ]
            ]);

            // Create default levels
            $levels = [
                ['name' => 'Ground Floor', 'order_index' => 1],
                ['name' => 'Level 1', 'order_index' => 2],
            ];

            foreach ($levels as $levelData) {
                $level = $project->levels()->firstOrCreate(
                    ['name' => $levelData['name']],
                    $levelData
                );

                // Create sample work areas
                $workAreas = [
                    ['name' => 'Living Room', 'type' => 'floor', 'category' => 'Apartment', 'frl' => '-/120/120'],
                    ['name' => 'Kitchen', 'type' => 'floor', 'category' => 'Apartment', 'frl' => '-/120/120'],
                    ['name' => 'Bedroom 1', 'type' => 'ceiling', 'category' => 'Apartment', 'frl' => '-/120/120'],
                    ['name' => 'Wall 0001', 'type' => 'wall', 'category' => 'Apartment', 'frl' => '-/120/120'],
                ];

                foreach ($workAreas as $workAreaData) {
                    $workAreaData['project_id'] = $project->id;
                    $workAreaData['level_id'] = $level->id;
                    
                    WorkArea::firstOrCreate(
                        [
                            'project_id' => $project->id,
                            'level_id' => $level->id,
                            'name' => $workAreaData['name']
                        ],
                        $workAreaData
                    );
                }
            }
        }
    }
}
