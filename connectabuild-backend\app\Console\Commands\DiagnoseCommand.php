<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Route;

class DiagnoseCommand extends Command
{
    protected $signature = 'connectabuild:diagnose';
    protected $description = 'Diagnostic complet de ConnectaBuild';

    public function handle()
    {
        $this->info('🔍 Diagnostic ConnectaBuild Backend');
        $this->info('==================================');
        $this->newLine();

        // 1. Environnement
        $this->info('1. Environnement Laravel:');
        $this->line('   - APP_ENV: ' . config('app.env'));
        $this->line('   - APP_DEBUG: ' . (config('app.debug') ? 'true' : 'false'));
        $this->line('   - APP_URL: ' . config('app.url'));
        $this->line('   - DB_CONNECTION: ' . config('database.default'));
        $this->newLine();

        // 2. Base de données
        $this->info('2. Base de données:');
        try {
            $userCount = User::count();
            $this->line("   ✅ Connexion DB OK - $userCount utilisateurs");
            
            $users = User::limit(5)->pluck('email');
            $this->line('   - Utilisateurs:');
            foreach ($users as $email) {
                $this->line("     * $email");
            }
        } catch (\Exception $e) {
            $this->error('   ❌ Erreur DB: ' . $e->getMessage());
        }
        $this->newLine();

        // 3. Routes API
        $this->info('3. Routes API:');
        $routes = Route::getRoutes();
        $apiRoutes = [];
        
        foreach ($routes as $route) {
            $uri = $route->uri();
            if (strpos($uri, 'api/') === 0) {
                $apiRoutes[] = implode('|', $route->methods()) . ' ' . $uri;
            }
        }
        
        if (empty($apiRoutes)) {
            $this->error('   ❌ Aucune route API trouvée');
        } else {
            $this->line('   ✅ Routes API trouvées (' . count($apiRoutes) . '):');
            foreach (array_slice($apiRoutes, 0, 10) as $route) {
                $this->line("     * $route");
            }
        }
        $this->newLine();

        // 4. Test authentification
        $this->info('4. Test d\'authentification:');
        try {
            $user = User::where('email', '<EMAIL>')->first();
            
            if (!$user) {
                $this->error('   ❌ Utilisateur <EMAIL> non trouvé');
            } else {
                $this->line('   ✅ Utilisateur trouvé: ' . $user->name);
                
                if (Hash::check('password123', $user->password)) {
                    $this->line('   ✅ Mot de passe correct');
                } else {
                    $this->error('   ❌ Mot de passe incorrect');
                }
                
                try {
                    $token = $user->createToken('test-token');
                    $this->line('   ✅ Création de token OK');
                } catch (\Exception $e) {
                    $this->error('   ❌ Erreur création token: ' . $e->getMessage());
                }
            }
        } catch (\Exception $e) {
            $this->error('   ❌ Erreur test auth: ' . $e->getMessage());
        }
        $this->newLine();

        // 5. Contrôleurs
        $this->info('5. Contrôleurs:');
        $controllers = [
            'App\Http\Controllers\Api\AuthController',
            'App\Http\Controllers\Api\ProjectController',
        ];

        foreach ($controllers as $controller) {
            if (class_exists($controller)) {
                $this->line("   ✅ $controller existe");
            } else {
                $this->error("   ❌ $controller manquant");
            }
        }
        $this->newLine();

        $this->info('Diagnostic terminé.');
        
        return 0;
    }
}
