import { But<PERSON> } from "@/components/ui/button";
import StatusDropdown from "@/components/StatusDropdown";

type Status = "Draft" | "Published" | "Completed";

interface Project {
  id: string;
  name: string;
  address: string;
  lastUpdated: string;
  status: Status;
  image?: string;
}

interface ProjectCardProps {
  project: Project;
  onStatusChange?: (projectId: string, status: Status) => void;
  onManage?: (projectId: string) => void;
}

const ProjectCard = ({ project, onStatusChange, onManage }: ProjectCardProps) => {
  return (
    <div className="project-card">
      <div className="flex items-center gap-4">
        {/* Project Image */}
        <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center overflow-hidden flex-shrink-0">
          {project.image ? (
            <img 
              src={project.image} 
              alt={project.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-8 h-8 bg-connecta-light-cyan rounded"></div>
          )}
        </div>

        {/* Project Info */}
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-foreground truncate">{project.name}</h3>
          <p className="text-muted-foreground text-sm truncate">{project.address}</p>
        </div>

        {/* Last Updated */}
        <div className="text-center min-w-[120px]">
          <p className="text-sm text-muted-foreground">Last Updated</p>
          <p className="text-sm font-medium text-foreground">{project.lastUpdated}</p>
        </div>

        {/* Status */}
        <div className="min-w-[120px]">
          <StatusDropdown
            currentStatus={project.status}
            onStatusChange={(status) => onStatusChange?.(project.id, status)}
          />
        </div>

        {/* Manage Button */}
        <div className="min-w-[100px]">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onManage?.(project.id)}
            className="w-full"
          >
            Manage
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ProjectCard;