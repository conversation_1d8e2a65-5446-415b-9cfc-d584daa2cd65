<?php

namespace Spa<PERSON>\QueryBuilder;

use <PERSON><PERSON>\LaravelPackageTools\Package;
use <PERSON><PERSON>\LaravelPackageTools\PackageServiceProvider;

class QueryBuilderServiceProvider extends PackageServiceProvider
{
    public function configurePackage(Package $package): void
    {
        $package
            ->name('laravel-query-builder')
            ->hasConfigFile();
    }

    public function registeringPackage(): void
    {
        $this->app->bind(QueryBuilderRequest::class, function ($app) {
            return QueryBuilderRequest::fromRequest($app['request']);
        });
    }

    public function provides(): array
    {
        return [
            QueryBuilderRequest::class,
        ];
    }
}
