<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Discipline;

class DisciplineSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $disciplines = [
            ['name' => 'Electrical', 'color' => '#FFD700'],
            ['name' => 'Plumbing', 'color' => '#0066CC'],
            ['name' => 'HVAC', 'color' => '#FF6600'],
            ['name' => 'Structural', 'color' => '#8B4513'],
            ['name' => 'Architectural', 'color' => '#32CD32'],
            ['name' => 'Fire Safety', 'color' => '#FF0000'],
            ['name' => 'Security', 'color' => '#800080'],
            ['name' => 'Telecommunications', 'color' => '#00CED1'],
        ];

        foreach ($disciplines as $discipline) {
            Discipline::firstOrCreate(
                ['name' => $discipline['name']],
                $discipline
            );
        }
    }
}
