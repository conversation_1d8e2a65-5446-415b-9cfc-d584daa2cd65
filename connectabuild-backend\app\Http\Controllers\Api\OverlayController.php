<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Overlay;
use App\Models\Level;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;

class OverlayController extends Controller
{
    /**
     * Display a listing of overlays for a specific level
     */
    public function index(Request $request, Level $level): JsonResponse
    {
        // Check if user has access to this level's project
        if (!$this->userHasAccessToProject($request->user(), $level->project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $overlays = $level->overlays()
            ->with(['discipline:id,name,color', 'creator:id,name,email'])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json(['overlays' => $overlays]);
    }

    /**
     * Store a newly created overlay
     */
    public function store(Request $request, Level $level): JsonResponse
    {
        // Check if user has access to this level's project
        if (!$this->userHasAccessToProject($request->user(), $level->project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'discipline_id' => 'required|exists:disciplines,id',
            'color' => 'nullable|string|max:7', // Hex color code
            'file' => 'required|file|mimes:pdf,dwg,dxf,png,jpg,jpeg|max:10240', // 10MB max
        ]);

        // Store the file
        $file = $request->file('file');
        $fileName = time() . '_' . $file->getClientOriginalName();
        $filePath = $file->storeAs('overlays', $fileName, 'public');

        // Create the overlay
        $overlay = Overlay::create([
            'level_id' => $level->id,
            'discipline_id' => $request->discipline_id,
            'name' => $request->name,
            'color' => $request->color,
            'file_path' => $filePath,
            'created_by' => $request->user()->id,
        ]);

        $overlay->load(['discipline:id,name,color', 'creator:id,name,email']);

        return response()->json([
            'overlay' => $overlay,
            'message' => 'Overlay created successfully',
        ], 201);
    }

    /**
     * Display the specified overlay
     */
    public function show(Request $request, Overlay $overlay): JsonResponse
    {
        // Check if user has access to this overlay's project
        if (!$this->userHasAccessToProject($request->user(), $overlay->level->project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $overlay->load(['discipline:id,name,color', 'creator:id,name,email', 'level:id,name']);

        return response()->json(['overlay' => $overlay]);
    }

    /**
     * Update the specified overlay
     */
    public function update(Request $request, Overlay $overlay): JsonResponse
    {
        // Check if user has access to this overlay's project
        if (!$this->userHasAccessToProject($request->user(), $overlay->level->project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'discipline_id' => 'sometimes|required|exists:disciplines,id',
            'color' => 'nullable|string|max:7',
            'file' => 'sometimes|file|mimes:pdf,dwg,dxf,png,jpg,jpeg|max:10240',
        ]);

        $updateData = $request->only(['name', 'discipline_id', 'color']);

        // Handle file update if provided
        if ($request->hasFile('file')) {
            // Delete old file
            if ($overlay->file_path) {
                Storage::disk('public')->delete($overlay->file_path);
            }

            // Store new file
            $file = $request->file('file');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('overlays', $fileName, 'public');
            $updateData['file_path'] = $filePath;
        }

        $overlay->update($updateData);
        $overlay->load(['discipline:id,name,color', 'creator:id,name,email']);

        return response()->json([
            'overlay' => $overlay,
            'message' => 'Overlay updated successfully',
        ]);
    }

    /**
     * Remove the specified overlay
     */
    public function destroy(Request $request, Overlay $overlay): JsonResponse
    {
        // Check if user has access to this overlay's project
        if (!$this->userHasAccessToProject($request->user(), $overlay->level->project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Delete the file
        if ($overlay->file_path) {
            Storage::disk('public')->delete($overlay->file_path);
        }

        $overlay->delete();

        return response()->json(['message' => 'Overlay deleted successfully']);
    }

    /**
     * Download the overlay file
     */
    public function download(Request $request, Overlay $overlay): JsonResponse
    {
        // Check if user has access to this overlay's project
        if (!$this->userHasAccessToProject($request->user(), $overlay->level->project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        if (!$overlay->file_path || !Storage::disk('public')->exists($overlay->file_path)) {
            return response()->json(['message' => 'File not found'], 404);
        }

        $filePath = Storage::disk('public')->path($overlay->file_path);
        $fileName = basename($overlay->file_path);

        return response()->download($filePath, $fileName);
    }

    /**
     * Check if user has access to a project
     */
    private function userHasAccessToProject($user, $project): bool
    {
        return $project->created_by === $user->id || 
               $project->members()->where('user_id', $user->id)->exists();
    }
}
