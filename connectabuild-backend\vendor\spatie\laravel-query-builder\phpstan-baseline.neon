parameters:
	ignoreErrors:
		-
			message: "#^Parameter \\#1 \\$keys of method Illuminate\\\\Support\\\\Collection\\<int,string\\>\\:\\:except\\(\\) expects array\\<int\\>\\|Illuminate\\\\Support\\\\Enumerable\\<\\(int\\|string\\), int\\>\\|string, int\\<\\-1, max\\> given\\.$#"
			count: 1
			path: src/Filters/FiltersExact.php

		-
			message: "#^Call to an undefined method ReflectionType\\:\\:getName\\(\\)\\.$#"
			count: 2
			path: src/Filters/FiltersScope.php

		-
			message: "#^Call to an undefined method ReflectionType\\:\\:isBuiltin\\(\\)\\.$#"
			count: 1
			path: src/Filters/FiltersScope.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<TModelClass of Illuminate\\\\Database\\\\Eloquent\\\\Model\\>\\:\\:onlyTrashed\\(\\)\\.$#"
			count: 1
			path: src/Filters/FiltersTrashed.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<TModelClass of Illuminate\\\\Database\\\\Eloquent\\\\Model\\>\\:\\:withTrashed\\(\\)\\.$#"
			count: 1
			path: src/Filters/FiltersTrashed.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<TModelClass of Illuminate\\\\Database\\\\Eloquent\\\\Model\\>\\:\\:withoutTrashed\\(\\)\\.$#"
			count: 1
			path: src/Filters/FiltersTrashed.php
