<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Project extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'address',
        'description',
        'cover_image',
        'status',
        'created_by',
    ];

    protected $casts = [
        'status' => 'string',
    ];

    /**
     * Get the user who created the project.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the levels for the project.
     */
    public function levels(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Level::class)->orderBy('order_index');
    }

    /**
     * Get the work areas for the project.
     */
    public function workAreas(): HasMany
    {
        return $this->hasMany(WorkArea::class);
    }

    /**
     * Get the project members.
     */
    public function projectMembers(): Has<PERSON><PERSON>
    {
        return $this->hasMany(ProjectMember::class);
    }

    /**
     * Get the users who are members of this project.
     */
    public function members(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'project_members')
                    ->withPivot(['role', 'is_service_provider', 'is_inspector', 'permissions', 'invited_at', 'joined_at'])
                    ->withTimestamps();
    }

    /**
     * Get the team invitations for the project.
     */
    public function teamInvitations(): HasMany
    {
        return $this->hasMany(TeamInvitation::class);
    }
}
