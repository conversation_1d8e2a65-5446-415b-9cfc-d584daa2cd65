{"name": "spatie/laravel-medialibrary", "description": "Associate files with Eloquent models", "license": "MIT", "keywords": ["spatie", "laravel-medialibrary", "media", "conversion", "images", "downloads", "cms", "laravel"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "homepage": "https://github.com/spatie/laravel-medialibrary", "require": {"php": "^8.2", "ext-exif": "*", "ext-fileinfo": "*", "ext-json": "*", "composer/semver": "^3.4", "illuminate/bus": "^10.2|^11.0|^12.0", "illuminate/conditionable": "^10.2|^11.0|^12.0", "illuminate/console": "^10.2|^11.0|^12.0", "illuminate/database": "^10.2|^11.0|^12.0", "illuminate/pipeline": "^10.2|^11.0|^12.0", "illuminate/support": "^10.2|^11.0|^12.0", "maennchen/zipstream-php": "^3.1", "spatie/image": "^3.3.2", "spatie/laravel-package-tools": "^1.16.1", "spatie/temporary-directory": "^2.2", "symfony/console": "^6.4.1|^7.0"}, "require-dev": {"ext-imagick": "*", "ext-pdo_sqlite": "*", "ext-zip": "*", "aws/aws-sdk-php": "^3.293.10", "guzzlehttp/guzzle": "^7.8.1", "league/flysystem-aws-s3-v3": "^3.22", "mockery/mockery": "^1.6.7", "larastan/larastan": "^2.7|^3.0", "orchestra/testbench": "^7.0|^8.17|^9.0|^10.0", "pestphp/pest": "^2.28|^3.5", "phpstan/extension-installer": "^1.3.1", "spatie/laravel-ray": "^1.33", "spatie/pdf-to-image": "^2.2|^3.0", "spatie/pest-plugin-snapshots": "^2.1"}, "conflict": {"php-ffmpeg/php-ffmpeg": "<0.6.1"}, "suggest": {"league/flysystem-aws-s3-v3": "Required to use AWS S3 file storage", "php-ffmpeg/php-ffmpeg": "Required for generating video thumbnails", "spatie/pdf-to-image": "Required for generating thumbnails of PDFs and SVGs"}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"Spatie\\MediaLibrary\\": "src"}}, "autoload-dev": {"psr-4": {"Spatie\\MediaLibrary\\Tests\\": "tests"}}, "config": {"allow-plugins": {"pestphp/pest-plugin": true, "phpstan/extension-installer": true}, "sort-packages": true}, "extra": {"laravel": {"providers": ["Spatie\\MediaLibrary\\MediaLibraryServiceProvider"]}}, "scripts": {"analyse": "vendor/bin/phpstan analyse", "baseline": "vendor/bin/phpstan analyse --generate-baseline", "test": "vendor/bin/pest"}}