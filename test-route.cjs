const axios = require('axios');

async function testRoute() {
  console.log('🧪 Test de la route test-login...\n');

  try {
    const response = await axios.post('http://localhost:8000/api/test-login', {
      email: '<EMAIL>',
      password: 'password123'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      }
    });

    console.log('✅ Succès !');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.log('❌ Erreur:');
    console.log('Status:', error.response?.status);
    console.log('Message:', error.message);
    console.log('Data:', error.response?.data);
  }
}

testRoute();
