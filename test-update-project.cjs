const axios = require('axios');

async function testUpdateProject() {
  console.log('🧪 Test de mise à jour du statut de projet...\n');

  try {
    // 1. Login pour obtenir un token
    console.log('1. Login...');
    const loginResponse = await axios.post('http://localhost:8000/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });

    const token = loginResponse.data.token;
    const user = loginResponse.data.user;
    console.log('✅ Login réussi pour:', user.name);

    // 2. Récupérer les projets pour avoir un ID valide
    console.log('\n2. Récupération des projets...');
    const projectsResponse = await axios.get('http://localhost:8000/api/projects', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json'
      }
    });

    const projects = projectsResponse.data.data;
    console.log(`✅ ${projects.length} projets trouvés`);

    if (projects.length === 0) {
      console.log('❌ Aucun projet disponible pour le test');
      return;
    }

    const firstProject = projects[0];
    console.log(`Premier projet: ${firstProject.name} (ID: ${firstProject.id}, Status: ${firstProject.status})`);

    // 3. Tester la mise à jour du statut
    console.log('\n3. Test de mise à jour du statut...');
    const newStatus = firstProject.status === 'draft' ? 'published' : 'draft';
    
    const updateResponse = await axios.put(`http://localhost:8000/api/projects/${firstProject.id}`, {
      status: newStatus
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      validateStatus: () => true // Accepter toutes les réponses
    });

    console.log('Status de la réponse:', updateResponse.status);
    console.log('Données de la réponse:', JSON.stringify(updateResponse.data, null, 2));

    if (updateResponse.status === 200) {
      console.log(`✅ Mise à jour réussie: ${firstProject.status} → ${newStatus}`);
    } else {
      console.log('❌ Échec de la mise à jour');
      console.log('Erreur:', updateResponse.data);
    }

  } catch (error) {
    console.log('❌ Erreur:');
    console.log('Message:', error.message);
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Data:', error.response.data);
    }
  }
}

testUpdateProject();
