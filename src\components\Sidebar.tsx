import { Home, Settings, LogOut, Layers } from "lucide-react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { cn } from "@/lib/utils";
import { useAuth } from "@/hooks/useAuth";
import connectaIcon from "@/assets/connecta-logo-icon.png";

const Sidebar = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout, isLoggingOut } = useAuth();

  const navigationItems = [
    {
      label: "Projects",
      href: "/projects",
      icon: Home,
      active: location.pathname === "/projects" || location.pathname === "/",
    },
    {
      label: "Overlays Test",
      href: "/overlays-test",
      icon: Layers,
      active: location.pathname === "/overlays-test",
    },
    {
      label: "Settings",
      href: "/settings",
      icon: Settings,
      active: location.pathname === "/settings",
    },
  ];

  return (
    <div className="w-64 bg-card border-r border-border h-screen flex flex-col">
      {/* Logo */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center gap-3">
          <img src={connectaIcon} alt="ConnectaBuild" className="w-8 h-8" />
          <h1 className="text-xl font-bold">
            <span className="text-connecta-navy">Connecta</span>
            <span className="text-connecta-cyan">Build</span>
          </h1>
        </div>
      </div>

      {/* User Greeting */}
      <div className="px-6 py-4">
        <p className="text-foreground font-medium">
          Hi {user?.name?.split(' ')[0] || 'User'},
        </p>
        {user?.company && (
          <p className="text-sm text-muted-foreground">
            {user.company.name}
          </p>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4">
        <ul className="space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <li key={item.href}>
                <Link
                  to={item.href}
                  className={cn(
                    "nav-item",
                    item.active && "active"
                  )}
                >
                  <Icon className="w-5 h-5" />
                  <span>{item.label}</span>
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-border">
        <button
          onClick={() => logout(undefined, {
            onSuccess: () => navigate('/login')
          })}
          disabled={isLoggingOut}
          className="nav-item w-full justify-start text-muted-foreground hover:text-destructive disabled:opacity-50"
        >
          <LogOut className="w-5 h-5" />
          <span>{isLoggingOut ? 'Logging out...' : 'Logout'}</span>
        </button>
        
        <div className="mt-4 text-xs text-muted-foreground">
          <p className="font-medium">Need Help? Contact us:</p>
          {user?.phone && (
            <p className="flex items-center gap-1 mt-1">
              📞 Mobile: {user.phone}
            </p>
          )}
          {user?.email && (
            <p className="flex items-center gap-1">
              ✉️ Email: {user.email}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default Sidebar;