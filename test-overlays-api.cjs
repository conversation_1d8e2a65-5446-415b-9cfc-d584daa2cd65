const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

async function testOverlaysAPI() {
  console.log('🧪 Test de l\'API Overlays...\n');

  try {
    // 1. Login pour obtenir un token
    console.log('1. Login...');
    const loginResponse = await axios.post('http://localhost:8000/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });

    const token = loginResponse.data.token;
    console.log('✅ Login réussi');

    // 2. Récupérer les projets pour avoir un niveau
    console.log('\n2. Récupération des projets...');
    const projectsResponse = await axios.get('http://localhost:8000/api/projects', {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    const projects = projectsResponse.data.data;
    if (projects.length === 0) {
      console.log('❌ Aucun projet disponible');
      return;
    }

    const firstProject = projects[0];
    console.log(`✅ Projet trouvé: ${firstProject.name} (ID: ${firstProject.id})`);

    // 3. Récupérer les niveaux du projet
    console.log('\n3. Récupération des niveaux...');
    const levelsResponse = await axios.get(`http://localhost:8000/api/projects/${firstProject.id}/levels`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    const levels = levelsResponse.data.levels;
    if (levels.length === 0) {
      console.log('❌ Aucun niveau disponible');
      return;
    }

    const firstLevel = levels[0];
    console.log(`✅ Niveau trouvé: ${firstLevel.name} (ID: ${firstLevel.id})`);

    // 4. Tester la récupération des overlays (devrait être vide)
    console.log('\n4. Test récupération des overlays...');
    const overlaysResponse = await axios.get(`http://localhost:8000/api/levels/${firstLevel.id}/overlays`, {
      headers: { 'Authorization': `Bearer ${token}` },
      validateStatus: () => true
    });

    console.log('Status:', overlaysResponse.status);
    if (overlaysResponse.status === 200) {
      console.log(`✅ Overlays récupérés: ${overlaysResponse.data.overlays.length} overlays`);
    } else {
      console.log('❌ Erreur récupération overlays:', overlaysResponse.data);
    }

    // 5. Créer un fichier PNG de test simple (1x1 pixel transparent)
    console.log('\n5. Création d\'un fichier PNG de test...');
    const testFileName = 'test-overlay.png';
    // PNG 1x1 transparent en base64
    const pngData = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==', 'base64');
    fs.writeFileSync(testFileName, pngData);

    // 6. Tester la création d'un overlay
    console.log('\n6. Test création d\'overlay...');
    const formData = new FormData();
    formData.append('name', 'Test Overlay');
    formData.append('discipline_id', '1'); // Electrical
    formData.append('color', '#FFD700');
    formData.append('file', fs.createReadStream(testFileName));

    const createResponse = await axios.post(
      `http://localhost:8000/api/levels/${firstLevel.id}/overlays`,
      formData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          ...formData.getHeaders()
        },
        validateStatus: () => true
      }
    );

    console.log('Status création:', createResponse.status);
    if (createResponse.status === 201) {
      console.log('✅ Overlay créé avec succès');
      console.log('Overlay:', createResponse.data.overlay);
    } else {
      console.log('❌ Erreur création overlay:', createResponse.data);
    }

    // Nettoyer le fichier de test
    fs.unlinkSync(testFileName);

  } catch (error) {
    console.log('❌ Erreur:', error.message);
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Data:', error.response.data);
    }
  }
}

testOverlaysAPI();
