import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Download, Trash2 } from 'lucide-react';
import DashboardLayout from '@/components/DashboardLayout';
import CreateOverlay from '@/components/CreateOverlay';
import { useOverlays, useDeleteOverlay } from '@/hooks/useOverlays';
import { overlayService } from '@/services/overlayService';

const OverlaysTest = () => {
  const [selectedLevelId, setSelectedLevelId] = useState<number>(1); // Ground Floor par défaut
  
  const { data: overlays, isLoading, error, refetch } = useOverlays(selectedLevelId);
  const deleteOverlayMutation = useDeleteOverlay();

  const handleDownload = async (overlay: any) => {
    try {
      const blob = await overlayService.downloadOverlay(overlay.id);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = overlay.name || 'overlay';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Download failed:', error);
      alert('Failed to download overlay');
    }
  };

  const handleDelete = (overlayId: number) => {
    if (confirm('Are you sure you want to delete this overlay?')) {
      deleteOverlayMutation.mutate(overlayId);
    }
  };

  return (
    <DashboardLayout>
      <div className="flex-1 p-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-foreground">Overlays Test</h1>
              <p className="text-muted-foreground mt-2">
                Test page for overlay creation and management
              </p>
            </div>
            <CreateOverlay 
              levelId={selectedLevelId} 
              onSuccess={() => refetch()}
            />
          </div>

          {/* Level Selection */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Level Selection</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-2">
                <Button
                  variant={selectedLevelId === 1 ? "default" : "outline"}
                  onClick={() => setSelectedLevelId(1)}
                >
                  Ground Floor (ID: 1)
                </Button>
                <Button
                  variant={selectedLevelId === 2 ? "default" : "outline"}
                  onClick={() => setSelectedLevelId(2)}
                >
                  Level 1 (ID: 2)
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Overlays List */}
          <Card>
            <CardHeader>
              <CardTitle>Overlays for Level {selectedLevelId}</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="w-6 h-6 animate-spin mr-2" />
                  Loading overlays...
                </div>
              ) : error ? (
                <div className="text-center py-8">
                  <p className="text-red-500 mb-4">Error loading overlays</p>
                  <Button onClick={() => refetch()} variant="outline">
                    Retry
                  </Button>
                </div>
              ) : overlays && overlays.length > 0 ? (
                <div className="space-y-4">
                  {overlays.map((overlay) => (
                    <div
                      key={overlay.id}
                      className="flex items-center justify-between p-4 border border-gray-200 rounded-lg"
                    >
                      <div className="flex items-center gap-4">
                        {/* Color indicator */}
                        <div
                          className="w-4 h-4 rounded-full border"
                          style={{ backgroundColor: overlay.color || overlay.discipline?.color }}
                        />
                        
                        {/* Overlay info */}
                        <div>
                          <h3 className="font-medium">{overlay.name}</h3>
                          <p className="text-sm text-gray-600">
                            {overlay.discipline?.name} • Created by {overlay.creator?.name}
                          </p>
                          <p className="text-xs text-gray-500">
                            {new Date(overlay.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDownload(overlay)}
                        >
                          <Download className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(overlay.id)}
                          disabled={deleteOverlayMutation.isPending}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500 mb-4">No overlays found for this level</p>
                  <p className="text-sm text-gray-400">
                    Click "Create Overlay" to add your first overlay
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Debug Info */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Debug Info</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <p><strong>Selected Level ID:</strong> {selectedLevelId}</p>
                <p><strong>Overlays Count:</strong> {overlays?.length || 0}</p>
                <p><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
                <p><strong>Error:</strong> {error ? 'Yes' : 'No'}</p>
                <p><strong>API URL:</strong> {import.meta.env.VITE_API_URL}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default OverlaysTest;
