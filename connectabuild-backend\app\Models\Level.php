<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Level extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'name',
        'order_index',
    ];

    /**
     * Get the project that owns the level.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the plans for the level.
     */
    public function plans(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Plan::class);
    }

    /**
     * Get the overlays for the level.
     */
    public function overlays(): HasMany
    {
        return $this->hasMany(Overlay::class);
    }

    /**
     * Get the work areas for the level.
     */
    public function workAreas(): HasMany
    {
        return $this->hasMany(WorkArea::class);
    }
}
